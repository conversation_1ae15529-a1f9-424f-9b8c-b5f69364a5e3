# Pangea Website

一個專業的錶盒相關網站，提供錶款展示、活動報名、部落格文章和客戶服務等功能。

## 📋 專案描述

Pangea Website 是一個現代化的錶盒展示與服務網站，整合了多項核心功能：

- **錶款展示**：精選錶款的詳細介紹與展示
- **活動報名**：錶匠體驗活動的線上報名系統
- **部落格系統**：基於 Google Sheets 的文章管理系統，支援 HTML 內容格式
- **客戶服務**：常見問題解答與客戶支援
- **錶盒預約**：專業的錶盒預約服務
- **金流整合**：支援信用卡與 ATM 轉帳付款

## 🛠 技術架構

### 核心技術棧

- **框架**：[Next.js 15](https://nextjs.org/) (App Router + Edge Runtime)
- **語言**：TypeScript
- **樣式**：[Tailwind CSS 4](https://tailwindcss.com/)
- **UI 組件**：[shadcn/ui](https://ui.shadcn.com/) + [Radix UI](https://www.radix-ui.com/)
- **圖標**：[Lucide React](https://lucide.dev/)
- **運行時**：Edge Runtime (完全兼容 Cloudflare Pages)

### 整合服務

- **金流**：PayUni (信用卡、ATM 轉帳)
- **資料管理**：Google Sheets (手錶、部落格、表單資料) + 本地檔案 (FAQ)
- **分析追蹤**：Google Tag Manager (GTM) + Meta Pixel
- **部署**：Cloudflare Pages (主要，Edge Runtime 優化)

### 開發工具

- **測試**：Jest + Testing Library + MSW + Playwright
- **程式碼品質**：ESLint + TypeScript
- **包管理**：npm

### 動畫系統
- **Motion.dev**：高效能動畫庫，支援 Cloudflare Pages
- **品牌一致性**：統一的動畫時長和緩動函數
- **無障礙設計**：支援 prefers-reduced-motion
- **效能優化**：硬體加速，避免重排重繪

## ⚡ Edge Runtime 架構

### Cloudflare Pages 優化
本專案已完全遷移到 **Edge Runtime**，專為 Cloudflare Pages 優化：

- **🚀 全域分發**：所有 API 路由在 Edge Runtime 上運行
- **⚡ 超快回應**：符合 Cloudflare Pages 10ms CPU 時間限制
- **🌍 無伺服器**：無需 Node.js 運行時，完全無伺服器架構
- **📊 統一資料源**：Google Sheets 作為主要資料管理平台

### 技術遷移重點
1. **部落格系統**：從 MDX 檔案系統遷移到 Google Sheets
2. **API 路由**：所有路由使用 `export const runtime = 'edge'`
3. **資料快取**：記憶體快取系統，支援 TTL 和命中率監控
4. **認證系統**：JWT 和加密功能完全兼容 Edge Runtime

## 🚀 環境設定

### 系統需求

- Node.js 20.0 或更高版本（建議使用 LTS 版本）
- npm 10.0 或更高版本

### 本地開發環境設置

1. **克隆專案**
   ```bash
   git clone <repository-url>
   cd pangea-website
   ```

2. **安裝依賴**
   ```bash
   npm install
   ```

3. **環境變數配置**

   複製環境變數範本：
   ```bash
   cp .env.example .env.local
   ```

4. **啟動開發伺服器**
   ```bash
   npm run dev
   ```

   開啟瀏覽器訪問 [http://localhost:3000](http://localhost:3000)

### 環境變數配置

使用 `APP_ENVIRONMENT` 統一控制所有服務環境
複製環境變數範本並配置必要的服務憑證：

```bash
cp .env.example .env.local
```

**主要環境變數：**
- **PayUni 金流**：`PAYUNI_ENVIRONMENT`, `PAYUNI_SANDBOX_*`, `PAYUNI_PRODUCTION_*`
- **Google Sheets**：`GOOGLE_SERVICE_ACCOUNT_EMAIL`, `GOOGLE_PRIVATE_KEY`, `GOOGLE_SHEET_ID`
- **分析追蹤**：`NEXT_PUBLIC_GTM_ID`, `META_PIXEL_ID`, `META_ACCESS_TOKEN`

**詳細配置指南：** [docs/environment-variables-guide.md](docs/environment-variables-guide.md)

## ✨ 功能特色

### 金流整合
- **PayUni 金流**：支援信用卡一次性付款和 ATM 轉帳
- **訂單管理**：完整的訂單狀態追蹤與查詢
- **付款重試**：支援付款失敗後的重新付款
- **自動對帳**：PayUni 回調自動更新訂單狀態

### 資料管理
- **混合式資料架構**：Google Sheets（動態資料）+ MDX 檔案（靜態內容）
- **部落格系統**：基於 MDX 的檔案式內容管理，支援 React 組件嵌入
- **即時同步**：表單提交即時寫入 Google Sheets
- **效能優化**：選擇性欄位讀取，減少 API 呼叫
- **智能快取系統**：多層次快取策略，自動預熱，顯著提升載入速度

### 分析追蹤
- **GTM 整合**：透過代理伺服器避免廣告攔截
- **Meta CAPI**：伺服器端事件追蹤
- **UTM 參數**：Facebook 廣告歸因追蹤
- **事件對應**：不同表單對應不同追蹤事件

### 使用者體驗
- **響應式設計**：支援桌面、平板、手機
- **無障礙設計**：符合 WCAG 標準
- **效能優化**：圖片懶載入、虛擬滾動
- **SEO 優化**：結構化資料、sitemap、robots.txt

## 🔒 安全性措施

### 防機器人攻擊
- **蜜罐欄位**：隱藏欄位檢測自動化提交
- **提交時間驗證**：檢測過快或過慢的提交
- **User Agent 分析**：識別可疑的請求來源
- **重複提交檢查**：防止短時間內重複提交

### 速率限制
- **IP 限制**：每分鐘 5 次請求
- **Email 限制**：每 5 分鐘 2 次提交
- **自動記錄**：失敗請求追蹤和分析

## 🧪 測試

專案採用多層次測試策略，確保核心業務流程的穩定性：

- **單元測試** - Jest + Testing Library（個別函數和組件）
- **整合測試** - Jest + MSW（API 端點和服務整合）

### 快速測試指令

```bash
# 執行所有測試
npm test

# 測試覆蓋率
npm run test:coverage

# API 測試套件
npm run test:api
```

**詳細測試指南：** [docs/testing-guide.md](docs/testing-guide.md)
**API 測試報告：** [API_TESTING_REPORT.md](API_TESTING_REPORT.md)
**CI/CD 配置：** [CI_CD_GUIDE.md](CI_CD_GUIDE.md)

## 🚀 部署

專案支援兩種主要的部署方案，各有其優勢和適用場景：

### 方案一：Cloudflare Pages (推薦)

**適合場景**：商業網站、高流量需求、成本敏感專案

**主要優勢**：
- ✅ **無限制流量**：適合網站成長需求
- ✅ **更低成本**：升級方案僅 $5/月 (vs Vercel $20/月)
- ✅ **強大安全性**：內建 DDoS 防護和 WAF
- ✅ **全球 CDN**：330+ 節點，台灣用戶延遲更低

**需要注意**：
- ⚠️ **CPU 限制較嚴格**：10ms/請求，需要程式碼優化
- ⚠️ **部署次數限制**：500次/月，開發期需注意

### 方案二：Vercel (備選)

**適合場景**：快速原型開發、團隊協作、Next.js 深度整合

**主要優勢**：
- ✅ **Next.js 原生支援**：最佳的開發體驗
- ✅ **寬鬆的函數限制**：更適合複雜邏輯處理
- ✅ **優秀的開發工具**：預覽部署、團隊協作功能

**需要注意**：
- ⚠️ **流量限制**：100GB/月，可能限制成長
- ⚠️ **較高成本**：升級方案 $20/月

### 快速部署指令

```bash
# Cloudflare Pages 部署
npm run build
npx wrangler pages deploy .next

# Vercel 部署
npm run build
npx vercel --prod
```

### 環境切換

使用 `APP_ENVIRONMENT` 環境變數統一控制所有服務：
- **測試環境**：設定為 `sandbox`
- **正式環境**：設定為 `production`

**詳細部署指南**：[docs/deployment-guide.md](docs/deployment-guide.md)

## 🗄️ 快取系統

### 架構特色
- **多層快取**：不同資料類型使用不同 TTL
- **效能監控**：即時追蹤命中率和回應時間
- **自動管理**：過期清理和預熱機制
- **管理介面**：`/api/admin/cache` 提供完整管理功能

### 快取配置

| 資料類型 | 快取時間 | 說明 |
|---------|---------|------|
| 部落格列表 | 建置時生成 | MDX 檔案在建置時預渲染 |
| 錶款資料 | 5 分鐘 | 商品資訊相對穩定 |
| FAQ 資料 | 10 分鐘 | 本地檔案，更新頻率最低 |
| 場次名額 | 30 秒 | 即時性要求高的資料 |
| 一般資料 | 5 分鐘 | 預設快取時間 |

**環境變數控制：**
```bash
# 快取配置（可選，有合理預設值）
CACHE_ENABLED=true           # 是否啟用快取（預設：true）
CACHE_TTL=300000            # 通用快取時間（毫秒，預設：5分鐘）
CACHE_MAX_SIZE=50           # 最大快取項目數（預設：50）
CACHE_LOGGING=false         # 生產環境建議關閉（開發環境自動啟用）

# 快取預熱機制
# 快取預熱已內建在應用啟動流程中，會在啟動後 1 秒自動執行
# 預熱內容包括：手錶列表、FAQ 資料（部落格已改為建置時生成）

### 快取管理

**手動清除快取：**
```bash
# 清除所有快取
curl -X DELETE /api/admin/cache

# 清除特定類型
curl -X DELETE /api/admin/cache?type=blog
```

**開發環境除錯：**
```javascript
// 瀏覽器控制台
__cacheManager.getStats()     // 查看快取統計
__cacheManager.clearCache()   // 清除所有快取
__cacheManager.warmupCache()  // 手動預熱
```

**快取監控：**
- 快取命中率統計
- 記憶體使用量監控
- 自動過期清理日誌
- 即時效能分析和建議

**快取配置工具：**
```bash
# 生成環境配置
npm run cache:config:dev     # 開發環境
npm run cache:config:prod    # 正式環境
npm run cache:config:all     # 所有環境

# 分析當前配置
npm run cache:analyze
```

**效能測試 API：**
```bash
# 快取效能測試
GET /api/cache-performance?action=test

# 基準測試
POST /api/cache-performance
{"action": "benchmark", "iterations": 10}

# 效能報告
GET /api/cache-performance?action=report
```

### 故障排除

**常見問題：**

1. **資料更新不及時**
   - 手動清除相關快取
   - 檢查快取時間設定

2. **記憶體使用過高**
   - 檢查快取項目數量（限制 50 個）
   - 確認定期清理機制運作正常

3. **效能問題**
   - 查看快取命中率
   - 檢查預熱機制是否正常執行

## 📁 專案結構

```
pangea-website/
├── src/
│   ├── app/                    # Next.js App Router 頁面
│   │   ├── api/               # API 路由
│   │   ├── blog/              # 部落格頁面
│   │   ├── pre-owned-watches/ # 錶款展示
│   │   ├── movement-assembling-booking/ # 錶匠體驗預約
│   │   ├── pangea-booking/    # 錶盒預約
│   │   └── support/           # 客戶支援
│   ├── components/            # React 組件
│   │   ├── ui/               # shadcn/ui 基礎組件
│   │   ├── mdx/              # MDX 專用組件
│   │   └── ...               # 業務組件
│   ├── lib/                   # 工具函數
│   ├── hooks/                 # React Hooks
│   ├── types/                 # TypeScript 類型定義
│   ├── utils/                 # 通用工具
│   ├── config/                # 配置檔案
│   └── __tests__/             # 測試檔案
├── content/                   # MDX 內容檔案
│   └── blog/                  # 部落格文章 (.mdx)
├── public/                    # 靜態資源
├── docs/                      # 專案文件
├── coverage/                  # 測試覆蓋率報告
└── scripts/                   # 建置腳本
```

### 主要目錄說明

- **`src/app/api/`**：後端 API 路由，處理表單提交、付款、資料查詢
- **`src/components/`**：可重用的 React 組件
- **`src/components/mdx/`**：MDX 專用組件（CTA、圖片、表格等）
- **`src/lib/`**：核心業務邏輯，如 PayUni 整合、Google Sheets 操作、快取系統
- **`src/lib/mdx-utils.ts`**：MDX 檔案處理工具
- **`src/hooks/`**：自定義 React Hooks
- **`content/blog/`**：MDX 格式的部落格文章

### 快取系統檔案

- **`src/lib/cache-implementation.ts`**：核心快取實作與 API 包裝
- **`src/lib/cache-management.ts`**：快取管理介面與操作
- **`src/lib/cache-warmup.ts`**：自動預熱機制
- **`src/app/api/admin/cache/`**：快取管理 API 端點

## 📝 MDX 部落格系統

### 系統特色

- **檔案式管理**：使用 MDX 檔案儲存文章內容，版本控制友善
- **React 組件支援**：可在 Markdown 中嵌入自定義 React 組件
- **自動化處理**：閱讀時間自動計算，SEO 資訊自動生成
- **靜態生成**：建置時預渲染所有文章，效能優異
- **類型安全**：完整的 TypeScript 支援

### 文章結構

每篇文章使用 MDX 格式，包含 frontmatter 和內容：

```mdx
---
title: "文章標題"
publishDate: "2025-01-01"
excerpt: "文章摘要"
thumbnail: "圖片網址"
tags: ["標籤1", "標籤2"]
seoTitle: "SEO 標題"
seoDescription: "SEO 描述"
author: "作者名稱"
slug: "文章網址"
---

文章內容使用 Markdown 語法...

<CallToAction
  buttonText="立即報名錶匠體驗"
  buttonUrl="/events"
/>
```

### 可用的 MDX 組件

- **`<CallToAction>`**：行動呼籲按鈕
- **`<ImageWithCaption>`**：帶說明文字的圖片
- **表格**：支援 GitHub Flavored Markdown 表格語法
- **程式碼高亮**：使用 rehype-highlight

### 新增文章流程

1. 在 `content/blog/` 目錄建立新的 `.mdx` 檔案
2. 檔名格式：`YYYY-MM-DD-slug.mdx`
3. 填寫 frontmatter 資訊
4. 撰寫文章內容
5. 執行 `npm run build` 測試建置
6. 提交程式碼，系統會自動部署

## 🔧 開發指令

```bash
# 開發
npm run dev              # 啟動開發伺服器 (port 3000)

# 建置
npm run build            # 建置正式版本
npm run start            # 啟動正式版本

# 程式碼品質
npm run lint             # ESLint 檢查

# 測試
npm test                 # 執行測試
npm run test:coverage    # 測試覆蓋率
npm run test:watch       # 監視模式測試
```

## 📚 相關文件

- **配置指南**：[環境變數配置](docs/environment-variables-guide.md)
- **開發指南**：[測試指南](docs/testing-guide.md) | [CI/CD 配置](CI_CD_GUIDE.md)
- **測試報告**：[API 測試報告](API_TESTING_REPORT.md)

## 🤝 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 授權

本專案為私有專案，版權所有。

## 📞 聯絡資訊

如有問題或建議，請聯繫開發團隊。
