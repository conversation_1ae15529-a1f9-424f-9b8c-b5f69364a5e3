# MDX 部落格系統使用指南

## 系統概述

本專案已從 Google Sheets CMS 遷移至 MDX 檔案系統，提供更好的內容管理體驗和開發者友好的工作流程。

## 檔案結構

```
content/blog/
├── 2024-10-22-is-watch-winder-necessary.mdx
├── 2024-11-21-omega-speedmaster-from-hesalite-to-sapphire.mdx
├── 2025-02-16-how-to-clean-bracelets-or-straps-of-watches.mdx
├── 2025-03-07-5-things-about-watch-water-resistance.mdx
└── 2025-04-29-in-house-movement-and-third-party-movement-guide.mdx
```

## Frontmatter 結構

每篇 MDX 文章都必須包含以下 frontmatter：

```yaml
---
title: "文章標題"
publishDate: "YYYY-MM-DD"
excerpt: "文章摘要"
thumbnail: "縮圖 URL"
tags: ["標籤1", "標籤2"]
seoTitle: "SEO 標題"
seoDescription: "SEO 描述"
author: "作者名稱"
readingTime: 5
slug: "url-slug"
---
```

## 可用的 MDX 組件

### CallToAction
行動呼籲按鈕組件

```jsx
<CallToAction 
  title="想了解更多機械錶知識？" 
  description="參加我們的錶匠體驗活動，親手體驗機芯的奧秘"
  buttonText="立即報名" 
  buttonUrl="/events"
/>
```

### ImageWithCaption
帶說明文字的圖片組件

```jsx
<ImageWithCaption 
  src="https://example.com/image.jpg" 
  alt="圖片替代文字" 
  caption="圖片說明文字"
/>
```

### InfoBox
資訊提示框組件

```jsx
<InfoBox type="info" title="提示">
這是一個資訊提示框的內容。
</InfoBox>
```

支援的類型：`info`, `warning`, `success`, `error`

### Table
表格組件

```jsx
<Table>
| 欄位1 | 欄位2 | 欄位3 |
|-------|-------|-------|
| 資料1 | 資料2 | 資料3 |
| 資料4 | 資料5 | 資料6 |
</Table>
```

## 新增文章流程

1. 在 `content/blog/` 目錄下建立新的 `.mdx` 檔案
2. 檔案命名格式：`YYYY-MM-DD-slug.mdx`
3. 添加完整的 frontmatter
4. 撰寫 Markdown 內容，可使用 MDX 組件
5. 確保 `slug` 欄位與檔案名稱一致

## 技術細節

- 使用 `next-mdx-remote` 進行 MDX 渲染
- 支援 TypeScript 類型檢查
- 自動生成 sitemap
- SEO 友好的 URL 結構
- 響應式設計支援

## 注意事項

- 所有圖片建議使用 CDN URL
- 確保 frontmatter 格式正確
- 測試文章在不同裝置上的顯示效果
- 定期檢查連結的有效性
