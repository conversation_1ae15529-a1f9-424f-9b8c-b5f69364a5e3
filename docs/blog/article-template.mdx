---
title: "文章標題範例"
publishDate: "2025-01-01"
excerpt: "這是文章摘要，簡短描述文章內容，建議控制在 100-150 字以內。這段摘要會顯示在部落格列表頁面和社群分享時使用。"
thumbnail: "https://example.com/your-image.jpg"
tags: ["機械錶", "保養", "選購指南"]
seoTitle: "文章標題範例 - Weaven Blog"
seoDescription: "這是 SEO 描述，用於搜尋引擎結果頁面顯示，建議控制在 150-160 字以內。"
socialImage: "https://example.com/your-social-image.jpg"
author: "Weaven"
readingTime: 5
---

# 文章標題

這是文章的開頭段落，用來介紹文章主題和吸引讀者繼續閱讀。建議在開頭就點出文章的核心價值和讀者能獲得的收穫。

## 主要章節標題

這裡是章節內容。你可以使用各種 Markdown 語法來豐富內容：

### 子章節標題

- 項目列表第一項
- 項目列表第二項
- 項目列表第三項

### 數字列表範例

1. 第一個步驟
2. 第二個步驟
3. 第三個步驟

## 圖片使用範例

你可以使用以下方式插入圖片：

<ImageWithCaption
  src="https://objectstorage.ap-tokyo-1.oraclecloud.com/n/nrlowkyadf71/b/webmedia/o/pre-owned-watches/1-min-cb0046db5ba3e92854648ec8d6607303"
  alt="Hesalite and Sapphire of Speedmaster"
  caption='圖片來源：<a href="https://www.watchfinder.co.uk/articles/review-omega-speedmaster-hesalite-vs-sapphire" target="_blank" rel="noopener noreferrer">Watchfinder &amp; Co.</a>'
/>


## 引用範例

> 這是一個引用區塊的範例。可以用來引用重要的觀點或名言。

## 程式碼範例

如果需要顯示程式碼，可以使用：

```javascript
// 這是程式碼範例
const example = "Hello World";
console.log(example);
```

行內程式碼可以這樣使用：`const variable = "value"`

## 連結範例

- [內部連結範例](/blog)
- [外部連結範例](https://www.example.com)

## 表格範例

| 項目 | 描述 | 價格 |
|------|------|------|
| 產品A | 產品A的描述 | $100 |
| 產品B | 產品B的描述 | $200 |
| 產品C | 產品C的描述 | $300 |

## 結語

文章的結尾段落，總結文章重點並可以包含行動呼籲（CTA）。

<CallToAction
  buttonText="立即報名錶匠體驗"
  buttonUrl="/experience"
/>

---

## 使用說明

### Frontmatter 欄位說明：

- **title**: 文章標題（必填）
- **publishDate**: 發布日期，格式為 YYYY-MM-DD（必填）
- **excerpt**: 文章摘要，用於列表頁面顯示（選填）
- **thumbnail**: 縮圖 URL（選填）
- **tags**: 標籤陣列（選填）
- **seoTitle**: SEO 標題（選填，預設使用 title）
- **seoDescription**: SEO 描述（選填，預設使用 excerpt）
- **socialImage**: 社群分享圖片（選填，預設使用 thumbnail）
- **author**: 作者（選填，預設為 "Weaven"）
- **readingTime**: 預估閱讀時間（分鐘）（選填，系統會自動計算）

### 檔案命名規則：

檔案名稱格式：`YYYY-MM-DD-article-slug.mdx`

例如：`2025-01-15-watch-maintenance-guide.mdx`

### 圖片使用建議：

1. 使用 `<ImageWithCaption>` 組件插入圖片
2. 建議圖片寬度設為 1200px
3. 務必包含 `alt` 屬性以提升無障礙性
4. 圖片 URL 建議使用 CDN 或雲端儲存服務

### 內容撰寫建議：

1. 使用清晰的標題結構（H1 > H2 > H3）
2. 段落保持適當長度，避免過長
3. 善用列表、引用等格式提升可讀性
4. 在適當位置加入圖片輔助說明
5. 結尾包含總結和行動呼籲

### 發布流程：

1. 複製此樣板檔案
2. 重新命名為適當的檔案名
3. 填寫 frontmatter 資訊
4. 撰寫文章內容
5. 檢查格式和連結
6. 提交並發布
