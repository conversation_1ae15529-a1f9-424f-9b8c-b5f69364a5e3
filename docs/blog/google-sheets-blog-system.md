# Google Sheets 部落格系統

本專案的部落格系統使用 Google Sheets 作為內容管理系統，完全兼容 Cloudflare Pages 的 Edge Runtime。

## 📋 系統概述

### 架構特點
- **Edge Runtime 兼容**：完全支援 Cloudflare Pages
- **無檔案系統依賴**：不需要讀取本地檔案
- **統一資料管理**：與手錶、表單資料使用相同的 Google Sheets 平台
- **HTML 內容支援**：支援豐富的 HTML 格式化內容

### 資料流程
```
Google Sheets (Blog 工作表) 
    ↓
Edge Runtime API (/api/blog)
    ↓
快取系統 (記憶體快取)
    ↓
前端頁面 (/blog, /blog/[slug])
```

## 🗂 Google Sheets 結構

### 工作表設定
- **工作表名稱**：`Blog` (英文) 或 `部落格` (中文)
- **資料範圍**：`A:L` (12 欄)
- **標題行**：第 1 行為欄位標題

### 欄位結構 (A:L)

| 欄位 | 名稱 | 類型 | 說明 |
|------|------|------|------|
| A | Title | 文字 | 文章標題 |
| B | Body | HTML | 文章內容 (支援 HTML) |
| C | Hero | HTML/URL | 主圖片 (HTML 或圖片 URL) |
| D | Author | HTML | 作者資訊 (支援 HTML) |
| E | Thumbnail | URL | 縮圖 URL |
| F | Time | 日期 | 發布時間 (YYYY-MM-DD 格式) |
| G | SEO:Slug | 文字 | 網址路徑 (如: `my-article`) |
| H | SEO:Title | 文字 | SEO 標題 |
| I | SEO:Description | 文字 | SEO 描述 |
| J | Social:Image | URL | 社群分享圖片 |
| K | Social:Title | 文字 | 社群分享標題 |
| L | Social:Description | 文字 | 社群分享描述 |

### 範例資料
```
Title: 如何清潔錶帶或錶鍊
Body: <p>錶帶和錶鍊的清潔是保養手錶的重要環節...</p>
Hero: https://example.com/hero-image.jpg
Author: <strong>Weaven 團隊</strong>
Thumbnail: https://example.com/thumbnail.jpg
Time: 2025-02-16
SEO:Slug: how-to-clean-bracelets-or-straps-of-watches
SEO:Title: 如何清潔錶帶或錶鍊 - Weaven Blog
SEO:Description: 學習正確的錶帶和錶鍊清潔方法...
Social:Image: https://example.com/social-image.jpg
Social:Title: 如何清潔錶帶或錶鍊
Social:Description: 專業的錶帶清潔指南
```

## 🔧 技術實作

### API 路由

#### 1. 部落格列表 API (`/api/blog`)
- **功能**：獲取所有部落格文章列表
- **支援分頁**：`?page=0&pageSize=9`
- **快取**：記憶體快取，TTL 10 分鐘
- **回應格式**：
```json
{
  "posts": [
    {
      "slug": "article-slug",
      "title": "文章標題",
      "thumbnail": "縮圖 URL",
      "publishDate": "2025-02-16T00:00:00.000Z",
      "author": "作者 HTML",
      "seoDescription": "SEO 描述"
    }
  ],
  "total": 10,
  "hasMore": false,
  "page": 0,
  "pageSize": 9,
  "totalPages": 2
}
```

#### 2. 部落格詳細 API (`/api/blog/[slug]`)
- **功能**：獲取單篇文章詳細內容
- **參數**：`slug` (來自 SEO:Slug 欄位)
- **回應格式**：
```json
{
  "post": {
    "slug": "article-slug",
    "title": "文章標題",
    "body": "文章 HTML 內容",
    "hero": "主圖片 HTML 或 URL",
    "author": "作者 HTML",
    "thumbnail": "縮圖 URL",
    "publishDate": "2025-02-16T00:00:00.000Z",
    "seoTitle": "SEO 標題",
    "seoDescription": "SEO 描述",
    "socialImage": "社群分享圖片",
    "socialTitle": "社群分享標題",
    "socialDescription": "社群分享描述"
  }
}
```

### 前端頁面

#### 1. 部落格列表頁 (`/blog`)
- **組件**：`BlogPage`
- **功能**：顯示文章列表，支援分頁
- **特色**：響應式設計，卡片式佈局

#### 2. 部落格詳細頁 (`/blog/[slug]`)
- **組件**：`BlogDetailContentEdge`
- **功能**：顯示完整文章內容
- **特色**：SEO 優化，社群分享支援

## 🚀 部署與維護

### 環境變數
```bash
# Google Sheets 設定
GOOGLE_BLOG_SHEET_ID=your_blog_sheet_id
GOOGLE_SANDBOX_BLOG_SHEET_ID=sandbox_blog_sheet_id
GOOGLE_PRODUCTION_BLOG_SHEET_ID=production_blog_sheet_id
```

### 快取管理
- **記憶體快取**：自動管理，TTL 10 分鐘
- **手動清除**：可透過 `/api/admin/cache` 清除
- **監控**：快取命中率和大小監控

### 內容管理
1. **新增文章**：在 Google Sheets 中新增一行
2. **編輯文章**：直接在 Google Sheets 中修改
3. **發布控制**：透過 Time 欄位控制發布時間
4. **SEO 優化**：填寫 SEO 相關欄位

## 🔄 從 MDX 遷移的變更

### 移除的功能
- ❌ MDX 檔案讀取
- ❌ 閱讀時間計算
- ❌ 靜態路徑生成
- ❌ MDX 組件支援

### 新增的功能
- ✅ Google Sheets 整合
- ✅ Edge Runtime 兼容
- ✅ HTML 內容支援
- ✅ 統一快取系統

### 保持的功能
- ✅ SEO 優化
- ✅ 社群分享
- ✅ 響應式設計
- ✅ 分頁功能
