# 部落格系統 (已遷移到 Google Sheets)

> ⚠️ **重要更新**：部落格系統已從 MDX 遷移到 Google Sheets，以支援 Cloudflare Pages 的 Edge Runtime。
>
> 本文檔保留作為歷史參考。目前的部落格系統使用 Google Sheets 作為資料來源。

## 遷移說明

### 從 MDX 到 Google Sheets
- **原因**：Cloudflare Pages 不支援 Node.js Runtime，無法讀取檔案系統
- **解決方案**：使用 Google Sheets 作為內容管理系統
- **優勢**：完全 Edge Runtime 兼容，全域分發，更快的回應時間

### 當前架構
- **資料來源**：Google Sheets (Blog 工作表，範圍 A:L)
- **內容格式**：HTML (支援豐富的格式化)
- **API 路由**：`/api/blog` 和 `/api/blog/[slug]`
- **頁面路由**：`/blog` 和 `/blog/[slug]`

---

# 原始 MDX 部落格系統文檔說明

## 概述

Pangea Website 的部落格系統基於 MDX (Markdown + JSX) 技術，提供強大的內容管理能力，支援在 Markdown 中嵌入 React 組件。

## 系統架構

### 核心技術

- **MDX**：Markdown + JSX，支援在文章中使用 React 組件
- **next-mdx-remote**：伺服器端 MDX 渲染
- **gray-matter**：frontmatter 解析
- **remark-gfm**：GitHub Flavored Markdown 支援（表格、刪除線等）
- **rehype-highlight**：程式碼語法高亮

### 檔案結構

```
content/
└── blog/
    ├── 2025-01-01-article-slug.mdx
    ├── 2025-01-02-another-article.mdx
    └── ...
```

## 文章格式

### Frontmatter 欄位

```yaml
---
title: "文章標題"                    # 必填：文章標題
publishDate: "2025-01-01"           # 必填：發布日期 (YYYY-MM-DD)
excerpt: "文章摘要"                  # 選填：文章摘要
thumbnail: "圖片網址"                # 選填：縮圖網址
tags: ["標籤1", "標籤2"]             # 選填：文章標籤
seoTitle: "SEO 標題"                # 選填：SEO 標題（預設使用 title）
seoDescription: "SEO 描述"          # 選填：SEO 描述
author: "作者名稱"                   # 選填：作者（預設 "Weaven"）
slug: "article-slug"                # 必填：文章網址
featured: false                     # 選填：是否為精選文章
---
```

### 自動化功能

- **閱讀時間**：系統自動計算，無需手動設定
- **發布日期**：可從檔名自動提取
- **SEO 資訊**：未設定時自動使用 title 和 excerpt

## 可用組件

### CallToAction 組件

```mdx
<CallToAction 
  buttonText="立即報名錶匠體驗" 
  buttonUrl="/events"
/>
```

### ImageWithCaption 組件

```mdx
<ImageWithCaption 
  src="圖片網址"
  alt="圖片描述"
  caption="圖片說明文字，支援 <a href='#'>連結</a>"
  width={1200}
/>
```

### 表格

支援標準 Markdown 表格語法：

```markdown
| 欄位1 | 欄位2 | 欄位3 |
|-------|-------|-------|
| 內容1 | 內容2 | 內容3 |
| 內容4 | 內容5 | 內容6 |
```

## 新增文章

### 1. 建立檔案

在 `content/blog/` 目錄建立新檔案：

```bash
# 檔名格式：YYYY-MM-DD-slug.mdx
touch content/blog/2025-01-15-new-article.mdx
```

### 2. 編寫內容

```mdx
---
title: "新文章標題"
publishDate: "2025-01-15"
excerpt: "這是一篇關於..."
thumbnail: "https://example.com/image.jpg"
tags: ["機械錶", "保養"]
slug: "new-article"
---

# 文章內容

這裡是文章的主要內容...

## 小標題

更多內容...

<CallToAction 
  buttonText="了解更多" 
  buttonUrl="/contact"
/>
```

### 3. 測試與部署

```bash
# 本地測試
npm run dev

# 建置測試
npm run build

# 提交程式碼
git add .
git commit -m "新增文章：新文章標題"
git push
```

## 開發指南

### 新增自定義組件

1. 在 `src/components/mdx/` 建立組件
2. 在 `src/components/MdxComponents.tsx` 註冊組件
3. 更新 TypeScript 類型定義

### 樣式自定義

- 全域樣式：`src/app/globals.css`
- 組件樣式：使用 Tailwind CSS 類別
- Prose 樣式：針對 `.prose` 類別自定義

### 效能優化

- 文章在建置時預渲染
- 圖片使用 Next.js Image 組件優化
- 自動生成靜態路徑

## 常見問題

### Q: 如何修改現有文章？

A: 直接編輯對應的 `.mdx` 檔案，提交後會自動重新部署。

### Q: 圖片如何處理？

A: 建議使用外部圖片服務（如 Oracle Cloud Storage），或放在 `public/images/` 目錄。

### Q: 如何設定精選文章？

A: 在 frontmatter 中設定 `featured: true`。

### Q: 標籤留空會怎樣？

A: 標籤區塊會自動隱藏，不會顯示在文章資訊中。

## 技術細節

### 檔案處理流程

1. `getAllMdxFiles()` - 掃描所有 MDX 檔案
2. `readMdxFile()` - 解析單個檔案
3. `calculateReadingTime()` - 計算閱讀時間
4. `MDXRemote` - 渲染 MDX 內容

### 路由結構

- `/blog` - 文章列表頁
- `/blog/[slug]` - 文章詳情頁
- `/api/blog` - 文章 API（向後相容）

### SEO 優化

- 自動生成 meta 標籤
- 結構化資料支援
- Open Graph 和 Twitter Card
- 自動 sitemap 生成
