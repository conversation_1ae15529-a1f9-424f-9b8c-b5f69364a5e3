'use client';

import { useState, useEffect } from 'react';

export interface WishlistItem {
  id: string;
  productName: string;
  brand: string;
  price: number;
  image?: string;
  slug: string;
}

const WISHLIST_STORAGE_KEY = 'pangea-wishlist';

export function useWishlist() {
  const [wishlist, setWishlist] = useState<WishlistItem[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  // 從 LocalStorage 載入鑑賞清單
  useEffect(() => {
    try {
      const stored = localStorage.getItem(WISHLIST_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setWishlist(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading wishlist from localStorage:', error);
      setWishlist([]);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // 監聽 wishlistUpdated 事件，重新載入資料
  useEffect(() => {
    const handleWishlistUpdate = () => {
      try {
        const stored = localStorage.getItem(WISHLIST_STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          setWishlist(Array.isArray(parsed) ? parsed : []);
        } else {
          setWishlist([]);
        }
      } catch (error) {
        console.error('Error reloading wishlist from localStorage:', error);
      }
    };

    window.addEventListener('wishlistUpdated', handleWishlistUpdate);
    return () => {
      window.removeEventListener('wishlistUpdated', handleWishlistUpdate);
    };
  }, []);

  // 儲存到 LocalStorage
  const saveToStorage = (items: WishlistItem[]) => {
    try {
      localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error);
    }
  };

  // 添加錶款到鑑賞清單
  const addToWishlist = (item: WishlistItem) => {
    setWishlist(prev => {
      // 檢查是否已存在
      const exists = prev.some(existing => existing.id === item.id);
      if (exists) {
        return prev;
      }

      const newWishlist = [...prev, item];
      saveToStorage(newWishlist);

      // 觸發更新事件
      setTimeout(() => {
        window.dispatchEvent(new Event('wishlistUpdated'));
      }, 0);

      return newWishlist;
    });
  };

  // 從鑑賞清單移除錶款
  const removeFromWishlist = (id: string) => {
    setWishlist(prev => {
      const newWishlist = prev.filter(item => item.id !== id);
      saveToStorage(newWishlist);

      // 觸發更新事件
      setTimeout(() => {
        window.dispatchEvent(new Event('wishlistUpdated'));
      }, 0);

      return newWishlist;
    });
  };

  // 清空鑑賞清單
  const clearWishlist = () => {
    setWishlist([]);
    saveToStorage([]);

    // 觸發更新事件
    setTimeout(() => {
      window.dispatchEvent(new Event('wishlistUpdated'));
    }, 0);
  };

  // 檢查錶款是否在鑑賞清單中
  const isInWishlist = (id: string) => {
    return wishlist.some(item => item.id === id);
  };

  // 取得鑑賞清單數量
  const wishlistCount = wishlist.length;

  return {
    wishlist,
    wishlistCount,
    isLoaded,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
  };
}
