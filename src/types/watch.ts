// 手錶資料類型定義，對應 Google Sheets 欄位結構
// 新的欄位順序：A~L 基本資料，M~R SEO，S~W 額外資料

export interface WatchData {
  // A~L: 基本資料欄位
  productName: string;           // A: Product Name
  brand: string;                 // B: Brand
  price: string;                 // C: Price
  thumbnail: string;             // D: Thumbnail (縮圖 URL)
  tag: string;                   // E: Tag (標籤說明)
  availability: string;          // F: Availability (成交狀態)
  boxAndPaper: string;           // G: Box & Paper
  movement: string;              // H: Movement
  caseSize: string;              // I: Case Size(mm)
  listingDescription: string;    // J: Listing Description
  image: string;                 // K: Image (主圖片 URL)
  cta: string;                   // L: CTA (粉專連結)
  // M~R: SEO 欄位
  seoSlug: string;               // M: SEO:Slug (網址路徑)
  seoTitle: string;              // N: SEO:Title (meta 標題)
  seoDescription: string;        // O: SEO:Description (meta 描述)
  socialImage: string;           // P: Social:Image (社群分享縮圖)
  socialTitle: string;           // Q: Social:Title (社群分享標題)
  socialDescription: string;     // R: Social:Description (社群分享描述)
  // S~W: 額外資料欄位
  serialNumber: string;          // S: Serial Number
  year: string;                  // T: Year
  dialType: string;              // U: Dial Type
  lugToLug: string;              // V: Lug to lug
  condition: string;             // W: Condition
}

// 前端使用的手錶類型（處理過的資料）
export interface Watch {
  id: string;                    // 由 row index 生成
  // A~L: 基本資料欄位
  productName: string;
  brand: string;
  price: number;                 // 轉換為數字
  thumbnail: string;
  tag: string;                   // 標籤說明
  availability: string;          // 成交狀態：已成交、銷售中、已預約
  boxAndPaper: string;
  movement: string;
  caseSize: string;
  listingDescription: string;
  image: string;
  cta: string;
  // M~R: SEO 欄位
  seoSlug: string;               // SEO:Slug (網址路徑)
  seoTitle: string;              // SEO:Title (meta 標題)
  seoDescription: string;        // SEO:Description (meta 描述)
  socialImage: string;           // Social:Image (社群分享縮圖)
  socialTitle: string;           // Social:Title (社群分享標題)
  socialDescription: string;     // Social:Description (社群分享描述)
  // S~W: 額外資料欄位
  serialNumber: string;
  year: string;
  dialType: string;
  lugToLug: string;
  condition: string;
}

// 列表頁面使用的簡化手錶類型（對應 A~F 欄位）
export interface WatchListItem {
  id: string;
  productName: string;           // A: Product Name
  brand: string;                 // B: Brand
  price: number;                 // C: Price (轉換為數字)
  thumbnail: string;             // D: Thumbnail
  tag: string;                   // E: Tag (標籤說明)
  availability: string;          // F: Availability (成交狀態)
  seoSlug: string;               // M: SEO:Slug (用於生成 URL)
}

// API 響應類型
export interface WatchesResponse {
  watches: WatchListItem[];
  brands: string[];
  total: number;
  hasMore: boolean;
  page?: number;
  pageSize?: number;
}

// 篩選和排序相關類型
export type AvailabilityStatus = '已成交' | '銷售中' | '已預約';

export type PriceRange = 'all' | 'under-50k' | '50k-100k' | '100k-200k' | 'over-200k';

export type SortOption = 'default' | 'price-asc' | 'price-desc';

export interface WatchFilters {
  brand: string;
  priceRange: PriceRange;
  sortBy: SortOption;
}

// 價格區間定義
export const PRICE_RANGES = {
  'all': { label: '全部價格', min: 0, max: Infinity },
  'under-50k': { label: '< NT$50,000', min: 0, max: 49999 },
  '50k-100k': { label: 'NT$50,000 - NT$100,000', min: 50000, max: 100000 },
  '100k-200k': { label: 'NT$100,000 - NT$200,000', min: 100000, max: 200000 },
  'over-200k': { label: '> NT$200,000', min: 200001, max: Infinity }
} as const;

// 排序選項定義
export const SORT_OPTIONS = {
  'default': { label: '預設排序' },
  'price-asc': { label: '價格由低到高' },
  'price-desc': { label: '價格由高到低' }
} as const;

// Google Sheets 欄位對應（新的欄位順序）
export const WATCH_SHEET_COLUMNS = {
  // A~L: 基本資料欄位
  PRODUCT_NAME: 'A',      // Product Name
  BRAND: 'B',             // Brand
  PRICE: 'C',             // Price
  THUMBNAIL: 'D',         // Thumbnail
  TAG: 'E',               // Tag
  AVAILABILITY: 'F',      // Availability
  BOX_AND_PAPER: 'G',     // Box & Paper
  MOVEMENT: 'H',          // Movement
  CASE_SIZE: 'I',         // Case Size(mm)
  LISTING_DESCRIPTION: 'J', // Listing Description
  IMAGE: 'K',             // Image
  CTA: 'L',               // CTA
  // M~R: SEO 欄位
  SEO_SLUG: 'M',          // SEO:Slug
  SEO_TITLE: 'N',         // SEO:Title
  SEO_DESCRIPTION: 'O',   // SEO:Description
  SOCIAL_IMAGE: 'P',      // Social:Image
  SOCIAL_TITLE: 'Q',      // Social:Title
  SOCIAL_DESCRIPTION: 'R', // Social:Description
  // S~W: 額外資料欄位
  SERIAL_NUMBER: 'S',     // Serial Number
  YEAR: 'T',              // Year
  DIAL_TYPE: 'U',         // Dial Type
  LUG_TO_LUG: 'V',        // Lug to lug
  CONDITION: 'W',         // Condition
} as const;

// 將 Google Sheets 原始資料轉換為 Watch 物件（對應新的欄位順序 A~W）
export function transformWatchData(rawData: string[], index: number): Watch {
  const [
    // A~L: 基本資料欄位
    productName = '',        // A: Product Name
    brand = '',              // B: Brand
    price = '0',             // C: Price
    thumbnail = '',          // D: Thumbnail
    tag = '',                // E: Tag
    availability = '',       // F: Availability
    boxAndPaper = '',        // G: Box & Paper
    movement = '',           // H: Movement
    caseSize = '',           // I: Case Size(mm)
    listingDescription = '', // J: Listing Description
    image = '',              // K: Image
    cta = '',                // L: CTA
    // M~R: SEO 欄位
    seoSlug = '',            // M: SEO:Slug
    seoTitle = '',           // N: SEO:Title
    seoDescription = '',     // O: SEO:Description
    socialImage = '',        // P: Social:Image
    socialTitle = '',        // Q: Social:Title
    socialDescription = '',  // R: Social:Description
    // S~W: 額外資料欄位
    serialNumber = '',       // S: Serial Number
    year = '',               // T: Year
    dialType = '',           // U: Dial Type
    lugToLug = '',           // V: Lug to lug
    condition = ''           // W: Condition
  ] = rawData;

  // 使用 row index 作為唯一 ID
  const uniqueId = `watch-${index}`;

  return {
    id: uniqueId,
    // A~L: 基本資料欄位
    productName,
    brand,
    price: parseFloat(price.replace(/[^0-9.-]+/g, '')) || 0, // 移除貨幣符號並轉換為數字
    thumbnail,
    tag,
    availability,
    boxAndPaper,
    movement,
    caseSize,
    listingDescription,
    image,
    cta,
    // M~R: SEO 欄位
    seoSlug,
    seoTitle,
    seoDescription,
    socialImage,
    socialTitle,
    socialDescription,
    // S~W: 額外資料欄位
    serialNumber,
    year,
    dialType,
    lugToLug,
    condition
  };
}

// 將 Watch 轉換為 WatchListItem
export function toWatchListItem(watch: Watch): WatchListItem {
  return {
    id: watch.id,
    productName: watch.productName,
    brand: watch.brand,
    price: watch.price,
    thumbnail: watch.thumbnail,
    tag: watch.tag,
    availability: watch.availability,
    seoSlug: watch.seoSlug
  };
}

// 將優化的 Google Sheets 資料（A~F 欄位 + M 欄位）直接轉換為 WatchListItem
export function transformWatchListData(rawData: string[], index: number): WatchListItem {
  const [
    productName = '',  // A: Product Name
    brand = '',        // B: Brand
    price = '0',       // C: Price
    thumbnail = '',    // D: Thumbnail
    tag = '',          // E: Tag
    availability = '', // F: Availability
    // 跳過 G~L 欄位
    , , , , , ,
    seoSlug = ''       // M: SEO:Slug
  ] = rawData;

  // 使用 row index 作為唯一 ID
  const uniqueId = `watch-${index}`;

  return {
    id: uniqueId,
    productName,
    brand,
    price: parseFloat(price.replace(/[^0-9.-]+/g, '')) || 0, // 移除貨幣符號並轉換為數字
    thumbnail,
    tag,
    availability,
    seoSlug
  };
}

// 品牌列表（用於篩選器）
export function getUniqueBrands(watches: Watch[]): string[] {
  const brands = watches.map(watch => watch.brand).filter(Boolean);
  return [...new Set(brands)].sort();
}

// 價格篩選函數
export function filterByPriceRange(watches: WatchListItem[], priceRange: PriceRange): WatchListItem[] {
  if (priceRange === 'all') return watches;

  const range = PRICE_RANGES[priceRange];
  return watches.filter(watch => watch.price >= range.min && watch.price <= range.max);
}

// 排序函數：已成交商品排在最後，預設排序下熱門款優先，價格排序時一起排
export function sortWatches(watches: WatchListItem[], sortBy: SortOption): WatchListItem[] {
  return [...watches].sort((a, b) => {
    // 1. 已成交的排在最後
    const aIsSold = a.availability === '已成交';
    const bIsSold = b.availability === '已成交';

    if (aIsSold && !bIsSold) return 1;
    if (!aIsSold && bIsSold) return -1;

    // 2. 在同一個 availability 群組內，按照用戶選擇的排序方式
    switch (sortBy) {
      case 'price-asc':
        return a.price - b.price;
      case 'price-desc':
        return b.price - a.price;
      case 'default':
      default:
        // 預設排序：有 Tag 的（熱門款）排在前面
        const aHasTag = a.tag && a.tag.trim() !== '';
        const bHasTag = b.tag && b.tag.trim() !== '';

        if (aHasTag && !bHasTag) return -1;
        if (!aHasTag && bHasTag) return 1;

        // 同樣有無 Tag 的情況下，保持原始順序
        return 0;
    }
  });
}
