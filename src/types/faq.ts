/**
 * 常見問題資料類型定義
 */
export interface FAQ {
  id: string;
  question: string;
  answer: string; // HTML 格式
  tags: string[]; // 支援多標籤
  rawTag?: string; // 原始標籤字串（用於除錯）
  category?: string; // 分類
  priority?: number; // 優先級
  lastUpdated?: string; // 最後更新時間
}

/**
 * 列表頁面使用的簡化常見問題類型（不包含 answer）
 */
export interface FAQListItem {
  id: string;
  question: string;
  tags: string[]; // 支援多標籤
  rawTag?: string; // 原始標籤字串（用於除錯）
  category?: string; // 分類
  priority?: number; // 優先級
  lastUpdated?: string; // 最後更新時間
}

/**
 * 轉換 Google Sheets 原始資料為 FAQ 物件
 * @param row - Google Sheets 的一列資料
 * @param index - 列索引（用於生成 ID）
 */
export function transformFAQData(row: (string | number | boolean | null)[], index: number): FAQ {
  // Google Sheets 欄位對應：
  // A: question
  // B: answer (HTML 格式)
  // C: tag (支援逗號分隔的多標籤)

  const question = String(row[0] || '').trim();
  const answer = String(row[1] || '').trim();
  const rawTag = String(row[2] || '').trim();

  // 解析多標籤：以逗號分隔，去除空格
  const tags = rawTag
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);

  return {
    id: `faq-${index}`,
    question,
    answer,
    tags,
    rawTag,
  };
}

/**
 * 將優化的 Google Sheets 資料（只包含列表需要的欄位）直接轉換為 FAQListItem
 * @param row - Google Sheets 的一列資料（只包含 question 和 tag）
 * @param index - 列索引（用於生成 ID）
 */
export function transformFAQListData(row: (string | number | boolean | null)[], index: number): FAQListItem {
  // Google Sheets 欄位對應（優化版本）：
  // 0: question (A欄)
  // 1: tag (C欄)

  const question = String(row[0] || '').trim();
  const rawTag = String(row[1] || '').trim();

  // 解析多標籤：以逗號分隔，去除空格
  const tags = rawTag
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);

  return {
    id: `faq-${index}`,
    question,
    tags,
    rawTag,
  };
}

/**
 * 將 FAQ 轉換為 FAQListItem
 */
export function toFAQListItem(faq: FAQ): FAQListItem {
  return {
    id: faq.id,
    question: faq.question,
    tags: faq.tags,
    rawTag: faq.rawTag,
  };
}
