'use client';

import { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import WatchCard from '@/components/WatchCard';
import WatchCardSkeleton from '@/components/WatchCardSkeleton';
import WatchFilters from '@/components/WatchFilters';
import { WatchesResponse, type WatchFilters as WatchFiltersType } from '@/types/watch';
import { useLazyLoading } from '@/hooks/useLazyLoading';
import { Shield, Calendar, Search, Handshake } from 'lucide-react';
import { useAnimationCompatibility } from '@/hooks/useBrowserDetection';

// 由於這是 client component，我們需要在父組件或 layout 中設定 metadata

const PreOwnedPage = () => {
  const [brands, setBrands] = useState<string[]>([]);
  const [filters, setFilters] = useState<WatchFiltersType>({
    brand: 'all',
    priceRange: 'all',
    sortBy: 'default'
  });

  // iOS 檢測和動畫兼容性
  useAnimationCompatibility();

  // 獲取手錶資料的函數
  const fetchWatchesData = useCallback(async (page: number, pageSize: number) => {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      brand: filters.brand,
      priceRange: filters.priceRange,
      sortBy: filters.sortBy
    });

    const response = await fetch(`/api/pre-owned-watches?${params}`);
    if (!response.ok) {
      throw new Error('無法獲取手錶資料');
    }

    const data: WatchesResponse = await response.json();

    // 更新品牌列表（只在第一次載入時）
    if (page === 0) {
      setBrands(data.brands);
    }

    return {
      items: data.watches,
      total: data.total,
      hasMore: data.hasMore
    };
  }, [filters]);

  // 使用 lazy loading hook
  const {
    items: watches,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    isLoadingMore
  } = useLazyLoading({
    fetchFunction: fetchWatchesData,
    pageSize: 9,
    initialLoad: true
  });

  // 篩選條件變更
  const handleFiltersChange = (newFilters: WatchFiltersType) => {
    setFilters(newFilters);
  };

  // 當篩選條件改變時重新載入
  useEffect(() => {
    refresh();
  }, [filters, refresh]);

  if (error) {
    return (
      <div className="min-h-screen bg-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl font-light mb-4" style={{ color: '#2b354d' }}>載入失敗</h1>
          <p className="mb-8" style={{ color: '#2b354d' }}>{error}</p>
          <button
            onClick={refresh}
            className="px-6 py-3 rounded-lg transition-colors"
            style={{ backgroundColor: '#2b354d', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            重新載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4" style={{ color: '#2b354d' }}>典藏錶款</h1>
            <p className="text-lg" style={{ color: '#2b354d' }}>對任何錶款有興趣歡迎直接私訊粉專洽談</p>
          </div>
        </div>
      </div>

      {/* 篩選和排序組件 */}
      <WatchFilters
        brands={brands}
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* 手錶列表 */}
      <section className="pt-4 pb-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading && watches.length === 0 ? (
            // 初始載入骨架屏 - 手機版兩欄，平板和桌面版三欄
            <div className="grid gap-4 sm:gap-6 lg:gap-8 grid-cols-2 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
              {Array.from({ length: 6 }).map((_, index) => (
                <WatchCardSkeleton key={index} />
              ))}
            </div>
          ) : watches.length === 0 ? (
            <div className="text-center py-20">
              <h3 className="text-2xl font-light mb-4" style={{ color: '#2b354d' }}>
                {filters.brand === 'all' ? '暫無符合條件的手錶' : `暫無符合條件的 ${filters.brand} 品牌手錶`}
              </h3>
              <p style={{ color: '#2b354d' }}>請嘗試調整篩選條件</p>
            </div>
          ) : (
            <>
              {/* 手錶網格 - 手機版兩欄，平板和桌面版三欄 */}
              <div className="grid gap-4 sm:gap-6 lg:gap-8 grid-cols-2 md:grid-cols-2 lg:grid-cols-3 watch-card-grid">
                {watches.map((watch) => (
                  <WatchCard key={watch.id} watch={watch} />
                ))}
              </div>

              {/* 載入更多內容的骨架屏 */}
              {isLoadingMore && (
                <div className="grid gap-4 sm:gap-6 lg:gap-8 grid-cols-2 md:grid-cols-2 lg:grid-cols-3 watch-card-grid mt-8">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <WatchCardSkeleton key={`loading-${index}`} />
                  ))}
                </div>
              )}

              {/* 查看更多按鈕 */}
              {hasMore && !isLoadingMore && (
                <div className="flex items-center justify-center mt-12">
                  <button
                    onClick={loadMore}
                    className="px-8 py-3 rounded-lg border border-slate-300 transition-all duration-200 hover:border-slate-400 hover:shadow-md"
                    style={{
                      backgroundColor: '#ffffff',
                      color: '#2b354d',
                      fontWeight: '500'
                    }}
                  >
                    查看更多手錶
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* 購買機制說明區塊 */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 主標題 */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ color: '#2b354d' }}>
              購買流程說明
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              了解我們的購買機制、錶況評估標準，以及多元化的付款方案
            </p>
          </div>

          {/* 三欄卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 購買機制卡片 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-video bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center relative">
                <Image
                  src="/images/placeholder-purchase-process.jpg"
                  alt="購買機制說明"
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling!.classList.remove('hidden');
                  }}
                />
                <div className="hidden text-6xl" style={{ color: '#2b354d' }}>🛒</div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3" style={{ color: '#2b354d' }}>
                  簡單購買流程
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  透過線上瀏覽、預約鑑賞、專業諮詢到安全交易，我們提供完整的購買體驗。每一步都有專業團隊協助，確保您能找到心儀的腕錶。
                </p>
              </div>
            </div>

            {/* 錶況介紹卡片 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-video bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center relative">
                <Image
                  src="/images/placeholder-watch-condition.jpg"
                  alt="錶況評估標準"
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling!.classList.remove('hidden');
                  }}
                />
                <div className="hidden text-6xl" style={{ color: '#2b354d' }}>🔍</div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3" style={{ color: '#2b354d' }}>
                  專業錶況評估
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  我們採用嚴格的錶況評估標準，從外觀、機芯到配件都有詳細檢測。每只腕錶都附有完整的狀況報告，讓您清楚了解錶款的真實狀態。
                </p>
              </div>
            </div>

            {/* USDT 購買方案卡片 */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-video bg-gradient-to-br from-yellow-50 to-orange-100 flex items-center justify-center relative">
                <Image
                  src="/images/placeholder-usdt-payment.jpg"
                  alt="USDT 付款方案"
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling!.classList.remove('hidden');
                  }}
                />
                <div className="hidden text-6xl" style={{ color: '#f59e0b' }}>💰</div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3" style={{ color: '#2b354d' }}>
                  USDT 數位支付
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  支援 USDT 等數位貨幣付款，提供更便利的國際交易方式。安全、快速、透明的區塊鏈技術，讓您的購買體驗更加順暢。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Weaven 安心購錶承諾區塊 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 主標題 */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4" style={{ color: '#2b354d' }}>
              Weaven 安心購錶承諾
            </h2>
          </div>

          {/* 承諾內容 - 2x2 網格佈局 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* 承諾一：100% 正品保證 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Shield className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    100% 正品保證
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    我們的資深錶匠團隊會對每一支腕錶的機芯與來源進行嚴格的交叉驗證，確保您收藏的每一件作品皆為原裝正品。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾二：一年機芯保固 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Handshake className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    一年機芯保固
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    我們為售出的每一支腕錶提供自購買日起一年的非人為機芯故障保固。所有保固服務皆由我們的專業團隊為您處理。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾三：詳實錶況說明 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Search className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    詳實錶況說明
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    手錶描述依原錶況如實說明，盡可能揭露所有使用痕跡，讓您在預約鑑賞前就充分掌握資訊。
                  </p>
                </div>
              </div>
            </div>

            {/* 承諾四：安全的預約鑑賞流程 */}
            <div className="bg-white rounded-lg p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <Calendar className="w-8 h-8" style={{ color: '#f59e0b' }} />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3" style={{ color: '#2b354d' }}>
                    安全的預約鑑賞流程
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    您的預約將由專人一對一服務。我們確保整個鑑賞與交易過程安全、私密且無壓力，您可以專注於尋找心儀的腕錶。
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 行動呼籲連結 */}
          <div className="text-center">
            <a
              href="/support/collection-warranty"
              className="inline-flex items-center text-lg font-medium transition-colors duration-200 hover:opacity-80"
              style={{ color: '#2b354d' }}
            >
              閱讀完整的典藏錶款保固政策
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PreOwnedPage;
