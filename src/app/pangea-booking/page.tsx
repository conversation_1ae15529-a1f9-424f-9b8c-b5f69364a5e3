'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

import { HoneypotField, TimestampField, SecurityTokenField } from '@/components/ui/honeypot-field';
import { useFormSecurity } from '@/hooks/useFormSecurity';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants, contentScrollVariants } from '@/lib/motion-config';
import { useAnimationCompatibility } from '@/hooks/useBrowserDetection';
import {
  ArrowRight
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';

// Pangea 預約表單資料介面
interface PangeaFormData {
  // 基本資料
  name: string;
  email: string;
  phone: string;
  gender: 'male' | 'female' | 'other' | '';

  // 預約資訊
  appointmentLocation: string;
  appointmentTime: string;
  age: '<20' | '20-29' | '30-39' | '40-49' | '50-59' | '>=60' | '';
  region: 'north' | 'central' | 'south' | 'offshore' | 'other_region' | '';

  // 手錶相關
  watchTypes: string[];
  watchBrands: string;
  questions: string;

  // 同意條款
  agreeToTerms: boolean;
}

export default function PangeaBookingPage() {
  // 使用統一的動畫兼容性 Hook
  useAnimationCompatibility();

  // 卡片動畫變體
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        delay: index * 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    })
  };

  // 特殊的中間卡片動畫（預約體驗價）
  const highlightCardVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.9,
      rotateY: -15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 0.8,
        delay: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  // 抓取 UTM 參數
  const [utmParams, setUtmParams] = useState({
    utm_campaign: '',
    utm_source_platform: '',
    utm_marketing_tactic: '',
    utm_creative_format: ''
  });

  const [formData, setFormData] = useState<PangeaFormData>({
    name: '',
    email: '',
    phone: '',
    gender: '',
    appointmentLocation: '',
    appointmentTime: '',
    age: '',
    region: '',
    watchTypes: [],
    watchBrands: '',
    questions: '',
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // 使用表單安全 Hook
  const {
    honeypotValue,
    formStartTime,
    securityToken,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  } = useFormSecurity();

  // 抓取 UTM 參數
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      setUtmParams({
        utm_campaign: urlParams.get('utm_campaign') || '',
        utm_source_platform: urlParams.get('utm_source') || '',
        utm_marketing_tactic: urlParams.get('utm_medium') || '',
        utm_creative_format: urlParams.get('utm_content') || ''
      });
    }
  }, []);

  // 預約地點選項
  const appointmentLocations = [
    '新北市三重區重新路一段108號3F',
    '台中邑達鐘錶（台中市北區漢口路四段388號）'
  ];

  // 預約時間選項
  const appointmentTimes = [
    '平日上午 (09:00-12:00)',
    '平日下午 (13:00-17:00)',
    '平日晚上 (18:00-21:00)',
    '週末上午 (09:00-12:00)',
    '週末下午 (13:00-17:00)',
    '週末晚上 (18:00-21:00)',
    '其他時間（請在備註說明）'
  ];

  // 手錶類型選項
  const watchTypeOptions = [
    '機械錶',
    '石英錶',
    '電子錶',
    '智慧錶',
    '無'
  ];

  // 表單驗證
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '請輸入姓名';
    }

    if (!formData.email.trim()) {
      newErrors.email = '請輸入電子郵件';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '請輸入有效的電子郵件地址';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '請輸入手機號碼';
    } else if (!/^(09\d{8}|886\d{9,10})$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = '請輸入有效的手機號碼';
    }

    if (!formData.gender) {
      newErrors.gender = '請選擇性別';
    }

    if (!formData.appointmentLocation) {
      newErrors.appointmentLocation = '請選擇預約地點';
    }

    if (!formData.appointmentTime) {
      newErrors.appointmentTime = '請選擇預約時間';
    }

    if (!formData.age) {
      newErrors.age = '請選擇年齡';
    }

    if (!formData.region) {
      newErrors.region = '請選擇居住地區';
    }

    if (formData.watchTypes.length === 0) {
      newErrors.watchTypes = '請選擇至少一種手錶類型';
    }

    if (!formData.watchTypes.includes('無') && formData.watchTypes.length > 0 && !formData.watchBrands.trim()) {
      newErrors.watchBrands = '請填寫手錶品牌';
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = '請同意服務條款';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 檢查表單是否有效
  useEffect(() => {
    const hasRequiredFields =
      formData.name.trim() &&
      formData.email.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.phone.trim() &&
      /^(09\d{8}|886\d{9,10})$/.test(formData.phone.replace(/\D/g, '')) &&
      formData.gender &&
      formData.appointmentLocation &&
      formData.appointmentTime &&
      formData.age &&
      formData.region &&
      formData.watchTypes.length > 0 &&
      formData.agreeToTerms &&
      (formData.watchTypes.includes('無') || formData.watchBrands.trim());

    setIsFormValid(Boolean(hasRequiredFields));
  }, [formData]);

  // 表單提交處理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // 安全驗證：檢查蜜罐欄位和提交時間
    const securityValidation = validateBeforeSubmit();
    if (!securityValidation.isValid) {
      alert(securityValidation.reason || '表單驗證失敗，請重新整理頁面後再試');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/pangea-appointment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          // 添加 UTM 參數
          utmParams,
          // 添加安全驗證資料
          security: {
            honeypotValue,
            formStartTime,
            securityToken,
            submissionTime: Date.now(),
            userAgent: navigator.userAgent,
          },
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert('預約申請已送出！我們會盡快與您聯繫安排體驗時間。');
        // 重置表單
        setFormData({
          name: '',
          email: '',
          phone: '',
          gender: '',
          appointmentLocation: '',
          appointmentTime: '',
          age: '',
          region: '',
          watchTypes: [],
          watchBrands: '',
          questions: '',
          agreeToTerms: false,
        });
        // 重置安全狀態
        resetSecurity();
      } else {
        throw new Error(result.error || '提交失敗');
      }
    } catch (error) {
      console.error('提交表單時發生錯誤:', error);
      alert('提交失敗，請稍後再試');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 處理表單欄位變更
  const handleInputChange = (field: keyof PangeaFormData, value: string | string[] | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除該欄位的錯誤訊息
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };



  return (
    <main className="min-h-screen">
      {/* Hero Section with Background Image */}
      <section className="relative min-h-[60vh] flex items-center justify-center">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/shop_cover_Pangea.jpg"
            alt="Pangea 機械錶智慧收藏盒"
            fill
            className="object-cover"
            priority
          />
          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <ScrollTriggerMotion
            variants={titleScrollVariants}
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
              PANGEA 機械錶智慧收藏盒預約體驗
            </h1>
            <p className="text-lg md:text-xl text-white/90 mb-8">
              填寫以下表單，我們將盡快與您聯繫安排體驗時間
            </p>
          </ScrollTriggerMotion>
        </div>
      </section>

      {/* Description Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollTriggerMotion
            variants={contentScrollVariants}
          >
            {/* 文字描述區塊 */}
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold mb-4" style={{ color: '#2b354d' }}>
                現場體驗 Pangea 機械錶智慧收藏盒
              </h2>
              <p className="text-xl font-bold mb-2" style={{ color: '#2b354d' }}>
                Make Every Tick Count
              </p>
              <p className="text-lg font-medium mb-8" style={{ color: '#f59e0b' }}>
                #DPFW
              </p>
            </div>

            {/* 三欄式佈局 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* 左欄：現貨狀態 */}
              <ScrollTriggerMotion
                variants={cardVariants}
                custom={0}
                className="text-center p-6 border border-gray-200 rounded-lg bg-white shadow-sm"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-[#2b354d] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">!</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                  限量現貨
                </h3>
                <p className="text-sm text-gray-600">
                  少量現貨開放訂購中
                </p>
              </ScrollTriggerMotion>

              {/* 中欄：優惠價格 */}
              <ScrollTriggerMotion
                variants={highlightCardVariants}
                className="text-center p-6 border-2 border-[#f59e0b] rounded-lg bg-white shadow-sm"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-[#f59e0b] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">$</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                  預約體驗價
                </h3>
                <p className="text-2xl font-bold" style={{ color: '#f59e0b' }}>
                  $53,000
                </p>
              </ScrollTriggerMotion>

              {/* 右欄：預約保留 */}
              <ScrollTriggerMotion
                variants={cardVariants}
                custom={2}
                className="text-center p-6 border border-gray-200 rounded-lg bg-white shadow-sm"
              >
                <div className="w-12 h-12 mx-auto mb-4 bg-[#2b354d] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">✓</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{ color: '#2b354d' }}>
                  優惠保留
                </h3>
                <p className="text-sm text-gray-600">
                  完整填寫預約問卷保留優惠名額
                </p>
              </ScrollTriggerMotion>
            </div>
          </ScrollTriggerMotion>
        </div>
      </section>

      {/* Appointment Form Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <ScrollTriggerMotion
            variants={contentScrollVariants}
          >
            <Card className="shadow-sm border border-gray-200">
              <CardContent className="p-6 md:p-8">
                <form className="space-y-6" onSubmit={handleSubmit}>
                    {/* 安全防護欄位 - 對使用者隱藏 */}
                    <HoneypotField
                      value={honeypotValue}
                      onChange={setHoneypotValue}
                    />
                    <TimestampField startTime={formStartTime} />
                    <SecurityTokenField token={securityToken} />

                    {/* 第一行：姓名 + Email */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name" className="text-sm font-medium text-[#2b354d]">
                          姓名 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="text"
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="王大明"
                          className={`mt-2 ${errors.name ? 'border-red-500' : ''}`}
                        />
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-500">{errors.name}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="email" className="text-sm font-medium text-[#2b354d]">
                          電子郵件 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="email"
                          id="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          className={`mt-2 ${errors.email ? 'border-red-500' : ''}`}
                        />
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    {/* 第二行：手機 + 性別 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="phone" className="text-sm font-medium text-[#2b354d]">
                          手機號碼 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="tel"
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="0912345678"
                          className={`mt-2 ${errors.phone ? 'border-red-500' : ''}`}
                        />
                        {errors.phone && (
                          <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
                        )}
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-[#2b354d]">
                          性別 <span className="text-red-500">*</span>
                        </Label>
                        <RadioGroup
                          value={formData.gender}
                          onValueChange={(value) => {
                            setFormData(prev => ({ ...prev, gender: value as 'male' | 'female' | 'other' | '' }));
                            if (errors.gender) {
                              setErrors(prev => ({ ...prev, gender: '' }));
                            }
                          }}
                          className="flex gap-4 mt-3"
                        >
                          <Label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                            <RadioGroupItem value="male" />
                            <span>男</span>
                          </Label>
                          <Label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                            <RadioGroupItem value="female" />
                            <span>女</span>
                          </Label>
                          <Label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                            <RadioGroupItem value="other" />
                            <span>其他</span>
                          </Label>
                        </RadioGroup>
                        {errors.gender && (
                          <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
                        )}
                      </div>
                    </div>

                    {/* 第三行：預約地點 + 預約時間 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="appointmentLocation" className="text-sm font-medium text-[#2b354d]">
                          預約地點 <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.appointmentLocation} onValueChange={(value) => handleInputChange('appointmentLocation', value)}>
                          <SelectTrigger className={`mt-2 ${errors.appointmentLocation ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="請選擇預約地點" />
                          </SelectTrigger>
                          <SelectContent>
                            {appointmentLocations.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.appointmentLocation && (
                          <p className="mt-1 text-sm text-red-500">{errors.appointmentLocation}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="appointmentTime" className="text-sm font-medium text-[#2b354d]">
                          預約時間 <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.appointmentTime} onValueChange={(value) => handleInputChange('appointmentTime', value)}>
                          <SelectTrigger className={`mt-2 ${errors.appointmentTime ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="請選擇預約時間" />
                          </SelectTrigger>
                          <SelectContent>
                            {appointmentTimes.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.appointmentTime && (
                          <p className="mt-1 text-sm text-red-500">{errors.appointmentTime}</p>
                        )}
                      </div>
                    </div>

                    {/* 第四行：年齡 + 居住地區 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="age" className="text-sm font-medium text-[#2b354d]">
                          年齡 <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.age} onValueChange={(value) => handleInputChange('age', value)}>
                          <SelectTrigger className={`mt-2 ${errors.age ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="請選擇年齡" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="<20">&lt;20</SelectItem>
                            <SelectItem value="20-29">20-29</SelectItem>
                            <SelectItem value="30-39">30-39</SelectItem>
                            <SelectItem value="40-49">40-49</SelectItem>
                            <SelectItem value="50-59">50-59</SelectItem>
                            <SelectItem value=">=60">&gt;=60</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.age && (
                          <p className="mt-1 text-sm text-red-500">{errors.age}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="region" className="text-sm font-medium text-[#2b354d]">
                          居住地區 <span className="text-red-500">*</span>
                        </Label>
                        <Select value={formData.region} onValueChange={(value) => handleInputChange('region', value)}>
                          <SelectTrigger className={`mt-2 ${errors.region ? 'border-red-500' : ''}`}>
                            <SelectValue placeholder="請選擇地區" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="north">北部地區（基、北、桃、竹、苗）</SelectItem>
                            <SelectItem value="central">中部地區（中、彰、雲、投）</SelectItem>
                            <SelectItem value="south">南部地區（嘉、南、高、屏）</SelectItem>
                            <SelectItem value="offshore">外島地區</SelectItem>
                            <SelectItem value="other_region">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.region && (
                          <p className="mt-1 text-sm text-red-500">{errors.region}</p>
                        )}
                      </div>
                    </div>



                    {/* 擁有的手錶類型 */}
                    <div>
                      <Label className="text-sm font-medium text-slate-700">
                        擁有的手錶類型 <span className="text-red-500">*</span>
                      </Label>
                      <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 mt-3">
                        {watchTypeOptions.map((option) => {
                          const handleToggle = () => {
                            const isChecked = formData.watchTypes.includes(option);
                            if (!isChecked) {
                              if (option === '無') {
                                // 如果選擇"無"，清除其他所有選項
                                setFormData(prev => ({
                                  ...prev,
                                  watchTypes: [option]
                                }));
                              } else {
                                // 如果選擇其他選項，移除"無"選項
                                setFormData(prev => ({
                                  ...prev,
                                  watchTypes: [...prev.watchTypes.filter(type => type !== '無'), option]
                                }));
                              }
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                watchTypes: prev.watchTypes.filter(type => type !== option)
                              }));
                            }
                          };

                          return (
                            <Label
                              key={option}
                              className="flex items-center p-3 border border-slate-200 rounded-lg hover:border-slate-300 hover:bg-slate-50 transition-all cursor-pointer group"
                            >
                              <Checkbox
                                checked={formData.watchTypes.includes(option)}
                                onCheckedChange={handleToggle}
                              />
                              <span className="ml-2 text-sm text-slate-700 group-hover:text-slate-900 transition-colors">{option}</span>
                            </Label>
                          );
                        })}
                      </div>
                      {errors.watchTypes && (
                        <p className="text-red-500 text-sm mt-3">{errors.watchTypes}</p>
                      )}
                    </div>

                    {/* 手錶品牌 */}
                    {!formData.watchTypes.includes('無') && formData.watchTypes.length > 0 && (
                      <div>
                        <Label className="text-sm font-medium text-slate-700">
                          目前擁有手錶的品牌 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="text"
                          placeholder="Rolex、Seiko......"
                          value={formData.watchBrands}
                          onChange={(e) => handleInputChange('watchBrands', e.target.value)}
                          className={`mt-2 ${errors.watchBrands ? 'border-red-500' : ''}`}
                        />
                        {errors.watchBrands && (
                          <p className="text-red-500 text-sm mt-2">{errors.watchBrands}</p>
                        )}
                      </div>
                    )}

                    {/* 問題或備註 */}
                    <div>
                      <Label htmlFor="questions" className="text-sm font-medium text-[#2b354d]">
                        對於預約體驗有任何疑問或特殊需求嗎？
                      </Label>
                      <Textarea
                        id="questions"
                        rows={4}
                        value={formData.questions}
                        onChange={(e) => handleInputChange('questions', e.target.value)}
                        placeholder="請描述您的問題或特殊需求..."
                        className="mt-2 resize-vertical"
                      />
                    </div>

                  {/* 同意條款 */}
                  <div>
                    <label className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={formData.agreeToTerms}
                        onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                        className="w-4 h-4 text-[#2b354d] border-gray-300 rounded focus:ring-[#2b354d] accent-[#2b354d] mt-1"
                      />
                      <span className="text-sm text-[#2b354d] leading-relaxed">
                        我同意提供個人資料用於預約體驗服務，並了解相關隱私權政策。 <span className="text-red-500">*</span>
                      </span>
                    </label>
                    {errors.agreeToTerms && (
                      <p className="mt-1 text-sm text-red-500">{errors.agreeToTerms}</p>
                    )}
                  </div>

                    {/* 提交按鈕 */}
                    <div className="pt-6">
                      <Button
                        type="submit"
                        disabled={!isFormValid || isSubmitting}
                        className={`w-full py-4 ${
                          isFormValid && !isSubmitting
                            ? 'bg-[#2b354d] hover:bg-[#1e2a3a] cursor-pointer'
                            : 'bg-gray-400 cursor-not-allowed'
                        }`}
                      >
                        {isSubmitting ? (
                          <div className="flex items-center justify-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                            提交中...
                          </div>
                        ) : (
                          <div className="flex items-center justify-center">
                            提交預約
                            <ArrowRight className="ml-2 h-5 w-5" />
                          </div>
                        )}
                      </Button>
                    </div>
                </form>
              </CardContent>
            </Card>
          </ScrollTriggerMotion>
        </div>
      </section>
    </main>
  );
}
