import HeroSection from "@/components/HeroSection";
import ImageHeroSection from "@/components/ImageHeroSection";
import FeatureSlider from "@/components/FeatureSlider";
import DesignGallerySection from "@/components/DesignGallerySection";
import ExpertTestimonialSection from "@/components/ExpertTestimonialSection";
import WatchCareHeroSection from "@/components/WatchCareHeroSection";
import FullscreenCarousel from "@/components/FullscreenCarousel";
import FullscreenImage from "@/components/FullscreenImage";
import ProductCTASection from "@/components/ProductCTASection";

export default function PangeaPage() {
  // Feature Images Data
  const featureImages = [
    {
      id: 1,
      image: "/images/features/feature1.webp",
      alt: "機芯檢測功能"
    },
    {
      id: 2,
      image: "/images/features/feature2.webp",
      alt: "智慧上鏈功能"
    },
    {
      id: 3,
      image: "/images/features/feature3.webp",
      alt: "手機顯示功能"
    }
  ];

  // Design Gallery Data
  const designGalleryImages = [
    {
      id: 1,
      image: "/images/design/design1.webp",
      alt: "跳脫傳統方盒設計"
    },
    {
      id: 2,
      image: "/images/design/design2.webp",
      alt: "座鐘擺放可收合腳架"
    },
    {
      id: 3,
      image: "/images/design/design3.webp",
      alt: "動力燈號顯示"
    },
    {
      id: 4,
      image: "/images/design/design4.webp",
      alt: "單顆獨立攜帶"
    }
  ];

  const designTextContent = [
    "跳脫傳統方盒設計",
    "149 件特規零件成就獨一無二",
    "可收合腳架巧思",
    "攜帶盒與座鐘輕鬆切換"
  ];

  // Expert Testimonial Data
  const expertTestimonialImages = [
    {
      id: 1,
      image: "/images/testimonials/expert1.webp",
      alt: "專業錶匠見證 1"
    },
    {
      id: 2,
      image: "/images/testimonials/expert2.webp",
      alt: "專業錶匠見證 2"
    },
    {
      id: 3,
      image: "/images/testimonials/expert3.webp",
      alt: "專業錶匠見證 3"
    },
    {
      id: 4,
      image: "/images/testimonials/expert4.webp",
      alt: "專業錶匠見證 4"
    }
  ];

  // Fullscreen Carousel 圖片資料
  const carouselSlides = [
    {
      id: 1,
      image: "/images/carousel/slide1.webp",
      alt: "手機介面展示 1"
    },
    {
      id: 2,
      image: "/images/carousel/slide2.webp",
      alt: "手機介面展示 2"
    }
  ];

  return (
    <main className="min-h-screen">
      {/* Video Hero Section */}
      <HeroSection
        title="全球第一台照顧機芯的錶盒"
        subtitle="PANGEA 機械錶智慧收藏盒"
      />

      {/* Image Hero Section */}
      <ImageHeroSection
        title="你的專屬錶匠"
        description={`我們和 30 年經驗老師傅合作打造
全球第一台照顧到機芯的收藏盒

掌握愛錶每一刻變化
時時刻刻照顧愛錶
讓享受機械錶變簡單

現在就與我們一起
在數位盤古大陸 PANGEA 上留下屬於你的足跡`}
        ctaText="立即預約體驗"
        ctaLink="/pangea-booking"
        backgroundImage="https://assets.softr-files.com/applications/78e28c86-637a-44cc-ac94-95ca3c49bdcc/assets/3202c104-936f-41ad-a54f-122b26a32d1a.webp"
      />

      {/* Feature Slider Section */}
      <FeatureSlider
        title="匠心獨具 | 三大功能"
        images={featureImages}
      />

      {/* Design Gallery Section */}
      <DesignGallerySection
        title="獨特設計巧思"
        textContent={designTextContent}
        images={designGalleryImages}
      />

      {/* Watch Care Hero Section */}
      <WatchCareHeroSection
        title="手錶是否該保養？"
        subtitle="當摔到手錶時， 90 % 以上的錶友無法知道機芯是否正常"
        ctaText="立即預約體驗"
        ctaLink="/pangea-booking"
        backgroundImage="/images/watch-care-hero.webp"
      />

      {/* Fullscreen Carousel */}
      <FullscreenCarousel
        slides={carouselSlides}
        interval={4000}
      />

      {/* Fullscreen Image */}
      <FullscreenImage
        image="/images/fullscreen-image.webp"
        alt="機構展示"
      />

      {/* Expert Testimonial Section */}
      <ExpertTestimonialSection
        title="專家見證"
        images={expertTestimonialImages}
        autoScroll={true}
        speed={50}
      />

      {/* Product CTA Section */}
      <ProductCTASection
        title="PANGEA 機械錶智慧收藏盒"
        subtitle="產品定價 $75,000 預約體驗獲得專屬優惠"
        ctaText="立即預約體驗"
        ctaLink="/pangea-booking"
        backgroundImage="/images/product-cta-bg.webp"
      />
    </main>
  );
}
