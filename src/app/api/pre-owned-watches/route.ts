import { NextResponse } from 'next/server';
import { getCachedWatchListData } from '@/lib/cache-implementation';
import {
  transformWatchListData,
  filterByPriceRange,
  sortWatches,
  type WatchListItem,
  type PriceRange,
  type SortOption
} from '@/types/watch';

/**
 * GET /api/pre-owned-watches
 * 獲取所有手錶資料（優化版本，只讀取列表頁面需要的欄位 A~F + M）
 * 支援品牌篩選、價格篩選和排序功能
 */

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const brand = searchParams.get('brand');
    const priceRange = (searchParams.get('priceRange') || 'all') as PriceRange;
    const sortBy = (searchParams.get('sortBy') || 'default') as SortOption;
    const page = parseInt(searchParams.get('page') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // 使用快取版本的讀取函數，只獲取列表頁面需要的欄位
    const rawData = await getCachedWatchListData();

    if (!rawData || rawData.length === 0) {
      return NextResponse.json({
        watches: [],
        brands: [],
        total: 0,
        hasMore: false
      });
    }

    // 跳過標題列，轉換資料為 WatchListItem
    const allWatches: WatchListItem[] = rawData
      .slice(1) // 跳過標題列
      .map((row, index) => transformWatchListData(row, index + 1))
      .filter(watch => watch.productName && watch.brand); // 過濾空資料

    // 應用篩選條件
    let filteredWatches = allWatches;

    // 品牌篩選
    if (brand && brand !== 'all') {
      filteredWatches = filteredWatches.filter(watch =>
        watch.brand.toLowerCase() === brand.toLowerCase()
      );
    }

    // 價格篩選
    filteredWatches = filterByPriceRange(filteredWatches, priceRange);

    // 排序（包含複雜的 Tag 優先、Availability 後置邏輯）
    filteredWatches = sortWatches(filteredWatches, sortBy);

    // 分頁處理
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const watches = filteredWatches.slice(startIndex, endIndex);
    const hasMore = endIndex < filteredWatches.length;

    // 獲取所有品牌列表
    const brands = [...new Set(allWatches.map(watch => watch.brand))].sort();

    const response = NextResponse.json({
      watches,
      brands,
      total: filteredWatches.length,
      hasMore,
      page,
      pageSize
    });

    return response;

  } catch (error) {
    console.error('獲取手錶資料失敗:', error);
    return NextResponse.json(
      { error: '無法獲取手錶資料' },
      { status: 500 }
    );
  }
}
