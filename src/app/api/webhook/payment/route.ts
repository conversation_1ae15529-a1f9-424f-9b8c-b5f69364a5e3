import { NextResponse } from 'next/server';
import { sendServerEvent } from '@/lib/meta-capi'; // Corrected import
import { parseWebhookResponseEdge } from '@/lib/payuni-edge';
import { clearSessionAvailabilityCache } from '@/lib/session-availability-cache';
import { getCurrentEnvironment } from '@/config/environment-config';
import { sha256 } from '@/lib/crypto-edge';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

/**
 * 取得 UTC+8 時區的時間字串
 */
function getUTC8TimeString(): string {
  const now = new Date();
  const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  return utc8Time.toISOString().replace('Z', '+08:00');
}

export async function POST(request: Request) {
  try {
    const timestamp = new Date().toISOString();
    console.log(`🔔 [${timestamp}] PayUni Webhook 收到請求`);
    console.log(`📍 請求來源: ${request.headers.get('user-agent')}`);
    console.log(`🌐 請求 URL: ${request.url}`);

    // PayUni sends notifications as form-data
    const formData = await request.formData();
    const encryptInfo = formData.get('EncryptInfo') as string;
    const hashInfo = formData.get('HashInfo') as string;

    // 記錄所有接收到的參數
    console.log('📥 Webhook 接收到的原始參數:', {
      EncryptInfo: encryptInfo ? `${encryptInfo.substring(0, 50)}...` : null,
      HashInfo: hashInfo ? `${hashInfo.substring(0, 50)}...` : null,
      allFormData: Object.fromEntries(formData.entries())
    });

    if (!encryptInfo || !hashInfo) {
      console.error('❌ Webhook Error: Missing EncryptInfo or HashInfo.');
      return NextResponse.json({ error: 'Missing required PayUni parameters' }, { status: 400 });
    }

    // Decrypt and validate the notification
    const paymentData = await parseWebhookResponseEdge(encryptInfo);

    if (!paymentData) {
      console.error('❌ Webhook Error: PayUni SHA256 validation failed.');
      return NextResponse.json({ error: 'PayUni Webhook validation failed' }, { status: 403 });
    }

    console.log('✅ PayUni Webhook 解密成功，付款通知數據:', JSON.stringify(paymentData, null, 2));

    // 記錄所有可能的狀態欄位
    console.log('🔍 檢查所有可能的狀態欄位:', {
      Status: paymentData.Status,
      TradeStatus: paymentData.TradeStatus,
      PaymentStatus: paymentData.PaymentStatus,
      State: paymentData.State,
      Result: paymentData.Result
    });

    // Extract necessary information from the decrypted data
    const {
      Status,
      TradeStatus,
      MerTradeNo,
      TradeNo,
      TradeAmt,
      PaymentType,
      PayerEmail,
      Message
    } = paymentData as {
      Status?: string | number;
      TradeStatus?: string | number;
      MerTradeNo?: string;
      TradeNo?: string;
      TradeAmt?: string | number;
      PaymentType?: string | number;
      PayerEmail?: string;
      Message?: string;
    };

    const orderNo = MerTradeNo;

    console.log('📋 解析後的關鍵欄位:', {
      Status: Status,
      StatusType: typeof Status,
      TradeStatus: TradeStatus,
      TradeStatusType: typeof TradeStatus,
      MerTradeNo: MerTradeNo,
      TradeNo: TradeNo,
      TradeAmt: TradeAmt,
      PaymentType: PaymentType,
      PaymentTypeType: typeof PaymentType,
      PayerEmail: PayerEmail,
      Message: Message
    });

    if (!orderNo) {
      console.error('❌ Webhook Error: Missing order number (MerTradeNo)');
      return NextResponse.json({ error: 'Missing order number' }, { status: 400 });
    }

    // Check if the payment was successful
    // PayUni 實際格式：
    // - Status="SUCCESS" 且 TradeStatus="1" 表示付款成功
    // - Status="SUCCESS" 且 TradeStatus="0" 且 PaymentType="2" 表示 ATM 取號成功
    // 根據 PayUni 官方文件：TradeStatus 0=ATM取號成功, 1=已付款, 2=付款失敗, 3=付款取消, 8=訂單待確認
    const isPaymentSuccess = (Status === 'SUCCESS' && TradeStatus === '1') || Status === '1';
    const isATMCreated = (Status === 'SUCCESS' && TradeStatus === '0' && PaymentType === '2');
    console.log(`🔍 檢查付款狀態: Status=${Status}, TradeStatus=${TradeStatus}, PaymentType=${PaymentType} -> 付款成功=${isPaymentSuccess}, ATM取號=${isATMCreated}`);

    if (isPaymentSuccess) {
      console.log(`✅ 處理成功付款，訂單: ${orderNo}`);

      // 更新 Google Sheets 中的訂單記錄
      try {
        console.log(`📝 開始更新 Google Sheets，訂單: ${orderNo}`);
        await updateOrderPaymentStatus(orderNo, {
          paymentStatus: '已完成',
          payuniTradeNo: TradeNo || '',
          paymentMethod: getPaymentMethodName(String(PaymentType || '')),
          paymentCompletedAt: getUTC8TimeString(),
        });
        console.log(`✅ 成功更新訂單 ${orderNo} 付款狀態`);

        // 清除場次名額快取，確保下次查詢時獲取最新資料
        clearSessionAvailabilityCache();
        console.log(`🗑️ 已清除場次名額快取`);
      } catch (updateError) {
        console.error(`❌ 更新訂單 ${orderNo} 失敗:`, updateError);
        console.error('❌ 錯誤詳情:', updateError instanceof Error ? updateError.stack : updateError);
        // 不要因為更新失敗而返回錯誤，避免 PayUni 重複發送通知
      }

      // Send Meta Conversion API event if email is available
      if (PayerEmail) {
        const hashedEmail = await sha256(PayerEmail);

        // We use a separate promise here as well
        sendServerEvent({
          event_name: 'Purchase', // 付款成功改為 Purchase 事件
          event_time: Math.floor(Date.now() / 1000),
          action_source: 'website',
          event_id: orderNo, // Use order number for deduplication
          user_data: {
            em: hashedEmail,
          },
          custom_data: {
            currency: 'TWD',
            value: Number(TradeAmt),
            order_id: orderNo,
          },
        }).catch(e => console.error('Failed to send CAPI event:', e));
      }

      // 發送成功回應給 PayUni
      return NextResponse.json({
        Status: 'SUCCESS',
        Message: '訂單處理成功'
      });

    } else if (isATMCreated) {
      console.log(`🏧 處理 ATM 取號成功，訂單: ${orderNo}`);

      // 更新 Google Sheets 中的訂單記錄
      try {
        console.log(`📝 開始更新 Google Sheets，ATM 取號成功，訂單: ${orderNo}`);
        await updateOrderPaymentStatus(orderNo, {
          paymentStatus: '待付款',
          payuniTradeNo: TradeNo || '',
          paymentMethod: 'ATM轉帳',
          paymentCompletedAt: '', // ATM 取號成功時不設定完成時間
        });
        console.log(`✅ 成功更新 ATM 取號訂單 ${orderNo} 狀態為待付款`);

        // 清除場次名額快取
        clearSessionAvailabilityCache();
        console.log(`🗑️ 已清除場次名額快取`);
      } catch (updateError) {
        console.error(`❌ 更新 ATM 取號訂單 ${orderNo} 失敗:`, updateError);
        console.error('❌ 錯誤詳情:', updateError instanceof Error ? updateError.stack : updateError);
      }

      // 發送成功回應給 PayUni
      return NextResponse.json({
        Status: 'SUCCESS',
        Message: 'ATM 取號處理成功'
      });

    } else {
      console.log(`⚠️ 收到非成功付款狀態，訂單 ${orderNo}: Status=${Status}, TradeStatus=${TradeStatus}, PaymentType=${PaymentType} - ${Message}`);

      // 根據 PayUni 官方文件處理其他狀態
      const tradeStatusStr = String(TradeStatus || '');

      // 處理其他狀態（付款失敗、取消、待確認等）
      if (tradeStatusStr === '2' || tradeStatusStr === '3' || tradeStatusStr === '8') {
        try {
          let statusText = '待付款';
          if (tradeStatusStr === '2') {
            statusText = '付款失敗';
          } else if (tradeStatusStr === '3') {
            statusText = '付款取消';
          } else if (tradeStatusStr === '8') {
            statusText = '訂單待確認';
          }

          console.log(`📝 更新訂單狀態為: ${statusText}`);
          await updateOrderPaymentStatus(orderNo, {
            paymentStatus: statusText,
            payuniTradeNo: TradeNo || '',
            paymentMethod: getPaymentMethodName(String(PaymentType || '')),
            paymentCompletedAt: statusText === '付款失敗' || statusText === '付款取消' ? getUTC8TimeString() : '',
          });
          console.log(`✅ 成功更新訂單 ${orderNo} 狀態為 ${statusText}`);

          // 清除場次名額快取
          clearSessionAvailabilityCache();
          console.log(`🗑️ 已清除場次名額快取`);
        } catch (updateError) {
          console.error(`❌ 更新訂單 ${orderNo} 狀態失敗:`, updateError);
        }
      } else {
        console.log(`⚠️ 未知的付款狀態: Status=${Status}, TradeStatus=${TradeStatus}，跳過更新`);
      }
    }

    // Respond to PayUni immediately to acknowledge receipt
    return NextResponse.json({ success: true, message: 'Webhook received' });

  } catch (error) {
    console.error('Webhook processing failed:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

// 報名狀態常數（AC 欄位）
const REGISTRATION_STATUS = {
  CONFIRMED: 1,     // 已確認（計入名額）
  RESERVED: 2,      // 保留中（計入名額）
  CANCELLED: 3      // 已取消（不計入名額，釋放名額）
} as const;

/**
 * 更新 Google Sheets 中的訂單付款狀態和報名狀態
 */
async function updateOrderPaymentStatus(
  orderNo: string,
  paymentInfo: {
    paymentStatus: string;
    payuniTradeNo: string;
    paymentMethod: string;
    paymentCompletedAt: string;
  }
) {
  const sheetName = '工作表1';
  console.log(`📊 開始更新 Google Sheets，訂單: ${orderNo}，付款資訊:`, paymentInfo);

  // 1. 讀取所有訂單資料
  console.log(`📖 讀取 Google Sheets 資料: ${sheetName}!A:AD`);
  const { getSheetDataEdge } = await import('@/lib/google-sheets-edge');
  const { GOOGLE_SHEETS_CONFIG } = await import('@/config/environment-config');
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();
  const sheetData = await getSheetDataEdge(sheetId, `${sheetName}!A:AD`);

  if (!sheetData || sheetData.length === 0) {
    console.error('❌ Google Sheets 沒有資料');
    throw new Error('找不到訂單資料');
  }

  console.log(`📋 Google Sheets 共有 ${sheetData.length} 行資料`);

  // 2. 找到對應的訂單行
  // 首先在 V 欄位（主訂單號碼）中查找
  console.log(`🔍 尋找訂單 ${orderNo} 在 V 欄 (索引 21)`);
  let orderRowIndex = sheetData.findIndex((row, index) => {
    const cellValue = row[21];
    console.log(`檢查行 ${index + 1}: V欄值 = "${cellValue}"`);
    return cellValue === orderNo;
  });

  // 如果在 V 欄位找不到，嘗試在 AB 欄位（重新付款訂單號碼）中查找
  if (orderRowIndex === -1) {
    console.log(`🔍 在 V 欄位找不到，嘗試在 AB 欄位（重新付款訂單號碼）中查找`);
    orderRowIndex = sheetData.findIndex((row, index) => {
      const cellValue = row[27]; // AB 欄位 (索引 27)
      console.log(`檢查行 ${index + 1}: AB欄值 = "${cellValue}"`);
      return cellValue === orderNo;
    });

    if (orderRowIndex !== -1) {
      console.log(`✅ 在 AB 欄位找到重新付款訂單: ${orderNo}`);
      // 這是重新付款的訂單，需要將新訂單號碼移動到主訂單號碼欄位
      await moveRetryOrderToMain(orderRowIndex + 1, orderNo);
    }
  }

  if (orderRowIndex === -1) {
    console.error(`❌ 在 Google Sheets 中找不到訂單: ${orderNo}`);
    console.log('📋 現有的訂單號碼:', sheetData.map((row, index) => `行${index + 1}: V="${row[21]}", AB="${row[27]}"`).filter(item => item.includes('"') && !item.includes('""')));
    throw new Error(`找不到訂單: ${orderNo}`);
  }

  // 3. 計算實際行號 (Google Sheets 從 1 開始計算)
  const actualRowNumber = orderRowIndex + 1;
  console.log(`✅ 找到訂單 ${orderNo} 在第 ${actualRowNumber} 行`);

  // 4. 使用 Google Sheets API 分別更新付款相關欄位
  const { updateSheetDataEdge } = await import('@/lib/google-sheets-edge');
  console.log(`📝 準備更新 Google Sheets 第 ${actualRowNumber} 行`);

  try {
    // 更新 W 欄位 (付款狀態)
    console.log(`📝 更新 W${actualRowNumber} 欄位 (付款狀態): ${paymentInfo.paymentStatus}`);
    await updateSheetDataEdge(sheetId, `${sheetName}!W${actualRowNumber}`, [[paymentInfo.paymentStatus]]);
    console.log(`✅ W 欄位更新成功`);

    // 更新 Y:AA 欄位 (PayUni交易號、付款方式、付款完成時間)
    console.log(`📝 更新 Y${actualRowNumber}:AA${actualRowNumber} 欄位:`, {
      Y: paymentInfo.payuniTradeNo,
      Z: paymentInfo.paymentMethod,
      AA: paymentInfo.paymentCompletedAt
    });
    await updateSheetDataEdge(sheetId, `${sheetName}!Y${actualRowNumber}:AA${actualRowNumber}`, [[
      paymentInfo.payuniTradeNo,      // Y: PayUni交易號
      paymentInfo.paymentMethod,      // Z: 付款方式
      paymentInfo.paymentCompletedAt, // AA: 付款完成時間
    ]]);
    console.log(`✅ Y:AA 欄位更新成功`);

    // 更新 AC 欄位 (報名狀態) - 根據付款狀態決定報名狀態
    let registrationStatus: number;
    if (paymentInfo.paymentStatus === '已完成') {
      registrationStatus = REGISTRATION_STATUS.CONFIRMED; // 付款完成 -> 已確認
    } else if (paymentInfo.paymentStatus === '待付款') {
      registrationStatus = REGISTRATION_STATUS.RESERVED; // 待付款 -> 保留中
    } else {
      registrationStatus = REGISTRATION_STATUS.CANCELLED; // 付款失敗/取消 -> 已取消
    }

    console.log(`📝 更新 AC${actualRowNumber} 欄位 (報名狀態): ${registrationStatus} (${getRegistrationStatusName(registrationStatus)})`);
    await updateSheetDataEdge(sheetId, `${sheetName}!AC${actualRowNumber}`, [[String(registrationStatus)]]);
    console.log(`✅ AC 欄位更新成功`);

    console.log(`🎉 成功更新訂單 ${orderNo} 在第 ${actualRowNumber} 行，付款資訊:`, paymentInfo, `報名狀態: ${getRegistrationStatusName(registrationStatus)}`);
  } catch (sheetsError) {
    console.error(`❌ Google Sheets API 更新失敗:`, sheetsError);
    throw sheetsError;
  }
}

/**
 * 取得報名狀態名稱
 */
function getRegistrationStatusName(status: number): string {
  switch (status) {
    case REGISTRATION_STATUS.CONFIRMED:
      return '已確認';
    case REGISTRATION_STATUS.RESERVED:
      return '保留中';
    case REGISTRATION_STATUS.CANCELLED:
      return '已取消';
    default:
      return '未知狀態';
  }
}

/**
 * 轉換 PayUni 付款方式代碼為中文名稱
 * 根據 PayUni 官方文件的付款方式代碼對應表
 */
function getPaymentMethodName(paymentType: string): string {
  const paymentCode = paymentType?.toString();

  switch (paymentCode) {
    case '1':
      return '信用卡';
    case '2':
      return 'ATM轉帳';
    case '3':
      return '代碼';
    case '5':
      return '貨到付款(超商取貨付款)';
    case '6':
      return '愛金卡 (iCash)';
    case '7':
      return '後支付 (Aftee)';
    case '9':
      return 'LinePay';
    case '10':
      return '宅配到付';
    case '11':
      return 'JKoPay';
    default:
      return paymentType || '未知';
  }
}

/**
 * 將重新付款的訂單號碼移動到主訂單號碼欄位
 * @param rowNumber - 行號（1-based）
 * @param newOrderNo - 新訂單號碼
 */
async function moveRetryOrderToMain(rowNumber: number, newOrderNo: string) {
  const sheetName = '工作表1';
  const { updateSheetDataEdge } = await import('@/lib/google-sheets-edge');
  const { GOOGLE_SHEETS_CONFIG } = await import('@/config/environment-config');
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();

  console.log(`📝 將重新付款訂單號碼移動到主欄位，行號: ${rowNumber}`);

  // 將 AB 欄位的值移動到 V 欄位
  await updateSheetDataEdge(sheetId, `${sheetName}!V${rowNumber}`, [[newOrderNo]]);

  // 清空 AB 欄位
  await updateSheetDataEdge(sheetId, `${sheetName}!AB${rowNumber}`, [['']]);

  console.log(`✅ 成功將重新付款訂單號碼移動到主欄位: ${newOrderNo}`);
}

/**
 * GET 端點用於測試 webhook URL 是否可達
 */
export async function GET(request: Request) {
  const timestamp = new Date().toISOString();
  console.log(`🔍 [${timestamp}] PayUni Webhook GET 測試請求`);

  return NextResponse.json({
    status: 'ok',
    message: 'PayUni Webhook endpoint is reachable',
    timestamp: timestamp,
    url: request.url,
    environment: getCurrentEnvironment(),
    notifyUrl: process.env.PAYUNI_NOTIFY_URL
  });
}
