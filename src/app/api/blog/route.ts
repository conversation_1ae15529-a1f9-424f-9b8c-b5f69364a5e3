import { NextResponse } from 'next/server';
import { getCachedBlogListData } from '@/lib/cache-implementation';

/**
 * GET /api/blog
 * 獲取所有部落格文章（MDX 版本）
 */

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '0');
    const pageSize = parseInt(searchParams.get('pageSize') || '12');

    // 從 Google Sheets 獲取部落格資料
    const blogData = await getCachedBlogListData();

    if (!blogData || blogData.length <= 1) { // 第一行是標題
      return NextResponse.json({
        posts: [],
        total: 0,
        hasMore: false,
        page: 0,
        pageSize: pageSize,
        totalPages: 0
      });
    }

    // 跳過標題行，轉換資料格式（符合原始 blog.ts 結構）
    const allPosts = blogData.slice(1).map((row, index) => {
      const [
        title = '',        // A: Title
        body = '',         // B: Body (content)
        hero = '',         // C: Hero
        author = '',       // D: Author
        thumbnail = '',    // E: Thumbnail
        time = '',         // F: Time (publishDate)
        seoSlug = '',      // G: SEO:Slug
        seoTitle = '',     // H: SEO:Title
        seoDescription = '', // I: SEO:Description
        socialImage = '',  // J: Social:Image
        socialTitle = '',  // K: Social:Title
        socialDescription = '' // L: Social:Description
      ] = row;

      // 如果沒有 seoSlug，使用 index 生成
      const finalSlug = seoSlug || `post-${index + 1}`;

      return {
        slug: finalSlug,
        title,
        thumbnail,
        publishDate: time ? new Date(time).toISOString() : new Date().toISOString(),
        author,
        seoDescription,
        body,
        hero,
        seoTitle,
        socialImage,
        socialTitle,
        socialDescription
      };
    }).filter(post => post.title && post.slug); // 過濾空資料

    // 分頁處理
    const total = allPosts.length;
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const posts = allPosts.slice(startIndex, endIndex);
    const hasMore = endIndex < total;
    const totalPages = Math.ceil(total / pageSize);

    const response = NextResponse.json({
      posts,
      total,
      hasMore,
      page,
      pageSize,
      totalPages
    });

    return response;

  } catch (error) {
    console.error('部落格 API 錯誤:', error);
    return NextResponse.json(
      { error: '無法載入部落格文章' },
      { status: 500 }
    );
  }
}
