import { NextRequest, NextResponse } from 'next/server';
import { queryPayUniOrderEdge, convertTradeStatus, convertPaymentType, getOverallPaymentStatus } from '@/lib/payuni-edge';
import { getSheetDataEdge } from '@/lib/google-sheets-edge';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderNo, email, website } = body;

    // Honeypot 驗證 - 如果 website 欄位被填入任何值（包括空白字符），則為機器人行為
    if (website && website !== '') {
      console.log('🤖 檢測到機器人行為 - honeypot 欄位被填入:', website);
      return NextResponse.json(
        { error: '請求參數錯誤' },
        { status: 400 }
      );
    }

    // 驗證必要參數
    if (!orderNo?.trim()) {
      return NextResponse.json(
        { error: '請輸入訂單號碼' },
        { status: 400 }
      );
    }

    if (!email?.trim()) {
      return NextResponse.json(
        { error: '請輸入電子郵件地址' },
        { status: 400 }
      );
    }

    // 驗證 email 格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return NextResponse.json(
        { error: '請輸入有效的電子郵件地址' },
        { status: 400 }
      );
    }

    console.log(`🔍 開始查詢訂單: ${orderNo}，Email: ${email}`);

    // 首先從 Google Sheets 查詢並驗證 email
    console.log('📊 從 Google Sheets 查詢訂單並驗證 email...');
    try {
      const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();
      const sheetData = await getSheetDataEdge(sheetId, '工作表1!A:AD');

      if (!sheetData || sheetData.length === 0) {
        console.log('❌ Google Sheets 沒有資料');
        return NextResponse.json(
          { error: '找不到指定的訂單' },
          { status: 404 }
        );
      }

      // 找到對應的訂單 (訂單號碼在 V 欄，索引 21)
      let orderRow = sheetData.find(row => row[21] === orderNo);

      // 如果在主訂單號碼中找不到，檢查是否在重新付款訂單號碼中（AB 欄位，索引 27）
      if (!orderRow) {
        orderRow = sheetData.find(row => row[27] === orderNo);
        if (orderRow) {
          console.log(`✅ 在重新付款訂單號碼中找到訂單: ${orderNo}`);
        }
      }

      if (!orderRow) {
        console.log(`❌ 在 Google Sheets 中找不到訂單: ${orderNo}`);
        return NextResponse.json(
          { error: '找不到指定的訂單' },
          { status: 404 }
        );
      }

      // 驗證 email 是否匹配 (email 在第 5 欄，索引 5)
      const orderEmail = orderRow[5]?.trim().toLowerCase();
      const inputEmail = email.trim().toLowerCase();

      if (!orderEmail || orderEmail !== inputEmail) {
        console.log(`❌ Email 不匹配 - 訂單 email: ${orderEmail}, 輸入 email: ${inputEmail}`);
        return NextResponse.json(
          { error: '電子郵件地址與訂單記錄不符，請確認後重試' },
          { status: 403 }
        );
      }

      console.log(`✅ Email 驗證通過，訂單存在於 Google Sheets`);

      // Email 驗證通過，繼續處理訂單資料

      // 檢查 Google Sheets 中的付款狀態
      const paymentStatus = orderRow[22] || ''; // W: 付款狀態
      console.log(`📊 Google Sheets 中的付款狀態: "${paymentStatus}"`);

      // 如果是重新付款中狀態，優先使用 Google Sheets 資料
      if (paymentStatus === '重新付款中') {
        console.log('🔄 檢測到重新付款中狀態，使用 Google Sheets 資料');
        return handleGoogleSheetsResult(orderRow, orderNo);
      }

      // 其他狀態，嘗試從 PayUni 獲取最新資料
      console.log('📡 嘗試從 PayUni API 獲取最新資料...');
      const payuniData = await queryPayUniOrderEdge(orderNo);

      // 如果 PayUni 查詢成功，使用 PayUni 資料
      if (payuniData && payuniData.Status === 'SUCCESS') {
        console.log('✅ PayUni API 查詢成功，使用 PayUni 資料');
        return handlePayUniResult(payuniData, orderNo);
      }

      // PayUni 查詢失敗，使用 Google Sheets 資料
      console.log('❌ PayUni API 查詢失敗，使用 Google Sheets 資料');
      return handleGoogleSheetsResult(orderRow, orderNo);

    } catch (sheetError) {
      console.error('❌ Google Sheets 查詢失敗:', sheetError);
      return NextResponse.json(
        { error: '查詢服務暫時不可用，請稍後再試' },
        { status: 503 }
      );
    }

  } catch (error) {
    console.error('處理訂單查詢請求時發生錯誤:', error);
    return NextResponse.json(
      { error: '伺服器錯誤，請稍後再試' },
      { status: 500 }
    );
  }
}

// 處理 PayUni API 查詢結果
function handlePayUniResult(payuniData: Record<string, unknown>, orderNo: string) {

    // 轉換 PayUni 資料為前端期望的格式
    const tradeStatus = String(payuniData['Result[0][TradeStatus]'] || '');
    const paymentType = String(payuniData['Result[0][PaymentType]'] || '');

    // 構建用於狀態判斷的資料物件
    const statusData = {
      TradeStatus: tradeStatus,
      RefundStatus: String(payuniData['Result[0][RefundStatus]'] || ''),
      RefundAmt: String(payuniData['Result[0][RefundAmt]'] || '0'),
      TradeAmt: String(payuniData['Result[0][TradeAmt]'] || '0')
    };

    // 使用綜合狀態判斷函數
    const paymentStatus = getOverallPaymentStatus(statusData);

    // 格式化付款完成時間
    let paymentCompletedAt = '';
    if (payuniData['Result[0][PaymentDay]'] && tradeStatus === '1') {
      paymentCompletedAt = String(payuniData['Result[0][PaymentDay]']);
    }

    // 建構回傳的訂單資料
    const orderData = {
      orderNo: String(payuniData['Result[0][MerTradeNo]'] || orderNo),
      name: '', // PayUni 查詢結果中沒有姓名資訊
      email: '', // PayUni 查詢結果中沒有 Email 資訊
      phone: '', // PayUni 查詢結果中沒有電話資訊
      sessionTimes: [], // PayUni 查詢結果中沒有場次資訊
      participationType: '', // PayUni 查詢結果中沒有參與類型資訊
      eventPrice: parseInt(String(payuniData['Result[0][TradeAmt]'] || '0')),
      submittedAt: String(payuniData['Result[0][CreateDay]'] || ''),
      paymentStatus,
      paymentMethod: convertPaymentType(paymentType),
      paymentCompletedAt,
      payuniTradeNo: String(payuniData['Result[0][TradeNo]'] || ''),
      payuniPayNo: String(payuniData['Result[0][OffPayNo]'] || ''),
      notes: '',
      eventName: '錶匠體驗',
      // 新增 PayUni 查詢的額外資訊
      tradeStatus: convertTradeStatus(tradeStatus),
      gateway: String(payuniData['Result[0][Gateway]'] || ''),
      dataSource: 'PayUni API', // 標記資料來源
      // 加入原始 PayUni 資料供顯示組件使用
      payuniRawData: payuniData
    };

    console.log(`✅ 成功查詢訂單 ${orderNo} (PayUni):`, {
      paymentStatus: orderData.paymentStatus,
      tradeStatus: orderData.tradeStatus,
      paymentMethod: orderData.paymentMethod,
      amount: orderData.eventPrice,
      dataSource: orderData.dataSource
    });

    return NextResponse.json(orderData);
}

// 處理 Google Sheets 查詢結果
function handleGoogleSheetsResult(orderRow: (string | number | boolean | null)[], orderNo: string) {
  // 解析 Google Sheets 資料
  // 欄位對應: A-提交時間, B-場次時間, C-自訂時間地點, D-參加方式, E-姓名, F-Email, G-手機, H-同行者姓名, I-同行者Email, J-性別, K-年齡, L-居住地區, M-手錶類型, N-手錶品牌, O-疑問, P-同意條款, Q-url, R-utm_campaign, S-utm_source_platform, T-utm_marketing_tactic, U-utm_creative_format, V-訂單號碼, W-付款狀態, X-應付金額, Y-PayUni交易號, Z-付款方式, AA-付款完成時間, AB-備註

  const orderData = {
    orderNo: orderRow[21] || orderNo,                    // V: 訂單號碼
    name: orderRow[4] || '',                             // E: 姓名
    email: orderRow[5] || '',                            // F: Email
    phone: orderRow[6] || '',                            // G: 手機
    sessionTimes: orderRow[1] ? String(orderRow[1]).split(', ') : [], // B: 場次時間（複選）
    participationType: orderRow[3] || '',                // D: 參加方式
    eventPrice: parseInt(String(orderRow[23] || '0')) || 0,             // X: 應付金額
    submittedAt: orderRow[0] || '',                      // A: 提交時間
    paymentStatus: orderRow[22] || '待付款',             // W: 付款狀態
    paymentMethod: orderRow[25] || '',                   // Z: 付款方式
    paymentCompletedAt: orderRow[26] || '',              // AA: 付款完成時間
    payuniTradeNo: orderRow[24] || '',                   // Y: PayUni交易號
    payuniPayNo: '',                                     // Google Sheets 中沒有此欄位
    notes: orderRow[27] || '',                           // AB: 備註
    eventName: '錶匠體驗',
    // Google Sheets 查詢的額外資訊
    tradeStatus: '', // Google Sheets 中沒有此欄位
    gateway: '',     // Google Sheets 中沒有此欄位
    dataSource: 'Google Sheets', // 標記資料來源
    // 額外的報名資訊
    customTimeLocation: orderRow[2] || '',               // C: 自訂時間地點
    companionName: orderRow[7] || '',                    // H: 同行者姓名
    companionEmail: orderRow[8] || '',                   // I: 同行者Email
    gender: orderRow[9] || '',                           // J: 性別
    age: orderRow[10] || '',                             // K: 年齡
    region: orderRow[11] || '',                          // L: 居住地區
    watchTypes: orderRow[12] ? String(orderRow[12]).split(', ') : [], // M: 手錶類型
    watchBrands: orderRow[13] || '',                     // N: 手錶品牌
    questions: orderRow[14] || '',                       // O: 疑問
    agreeToTerms: orderRow[15] === '是',                 // P: 同意條款
    utmParams: {                                         // UTM 參數
      utm_campaign: orderRow[17] || '',                  // R: utm_campaign
      utm_source_platform: orderRow[18] || '',          // S: utm_source_platform
      utm_marketing_tactic: orderRow[19] || '',         // T: utm_marketing_tactic
      utm_creative_format: orderRow[20] || ''           // U: utm_creative_format
    }
  };

  console.log(`✅ 成功查詢訂單 ${orderNo} (Google Sheets):`, {
    paymentStatus: orderData.paymentStatus,
    paymentMethod: orderData.paymentMethod,
    amount: orderData.eventPrice,
    dataSource: orderData.dataSource,
    name: orderData.name,
    email: orderData.email
  });

  return NextResponse.json(orderData);
}
