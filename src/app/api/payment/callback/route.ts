import { NextRequest, NextResponse } from 'next/server';

/**
 * PayUni 付款結果回調 API
 * 處理 PayUni 的 POST 返回，然後重定向到結果頁面
 */

// Edge Runtime 配置 - Cloudflare Pages 需要
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 PayUni POST 回調收到請求');
    
    // 獲取 PayUni 返回的參數
    const formData = await request.formData();
    const urlParams = new URLSearchParams();
    
    // 將所有表單數據轉換為 URL 參數
    for (const [key, value] of formData.entries()) {
      if (typeof value === 'string') {
        urlParams.append(key, value);
        console.log(`📝 參數: ${key} = ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
      }
    }
    
    // 也檢查 URL 查詢參數
    const url = new URL(request.url);
    for (const [key, value] of url.searchParams.entries()) {
      urlParams.append(key, value);
      console.log(`🔗 URL 參數: ${key} = ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
    }
    
    // 構建重定向 URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const redirectUrl = `${baseUrl}/payment/result?${urlParams.toString()}`;
    
    console.log('🎯 重定向到:', redirectUrl.substring(0, 200) + '...');
    
    // 重定向到結果頁面
    return NextResponse.redirect(redirectUrl, 302);
    
  } catch (error) {
    console.error('❌ PayUni 回調處理失敗:', error);
    
    // 發生錯誤時重定向到失敗頁面
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const errorUrl = `${baseUrl}/payment/result?Status=FAILED&Error=CallbackError`;
    
    return NextResponse.redirect(errorUrl, 302);
  }
}

/**
 * 也支援 GET 方法，以防 PayUni 使用 GET 返回
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔄 PayUni GET 回調收到請求');
    
    const url = new URL(request.url);
    const urlParams = new URLSearchParams();
    
    // 獲取所有查詢參數
    for (const [key, value] of url.searchParams.entries()) {
      urlParams.append(key, value);
      console.log(`🔗 參數: ${key} = ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
    }
    
    // 構建重定向 URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const redirectUrl = `${baseUrl}/payment/result?${urlParams.toString()}`;
    
    console.log('🎯 重定向到:', redirectUrl.substring(0, 200) + '...');
    
    // 重定向到結果頁面
    return NextResponse.redirect(redirectUrl, 302);
    
  } catch (error) {
    console.error('❌ PayUni 回調處理失敗:', error);
    
    // 發生錯誤時重定向到失敗頁面
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const errorUrl = `${baseUrl}/payment/result?Status=FAILED&Error=CallbackError`;
    
    return NextResponse.redirect(errorUrl, 302);
  }
}
