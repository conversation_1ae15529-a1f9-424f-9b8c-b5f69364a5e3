'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRight, Mail, MessageSquare } from 'lucide-react';
import { useFormSecurity } from '@/hooks/useFormSecurity';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants } from '@/lib/motion-config';
import { useIsIOSSafari } from '@/hooks/useBrowserDetection';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { HoneypotField, TimestampField, SecurityTokenField } from '@/components/ui/honeypot-field';

interface FormData {
  name: string;
  email: string;
  phone: string;
  contactMethod: 'email' | 'phone';
  topic: string;
  message: string;
}

export default function ContactPage() {
  const { isIOSSafari } = useIsIOSSafari();

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    contactMethod: 'email',
    topic: '',
    message: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // 安全保護
  const {
    honeypotValue,
    formStartTime,
    securityToken,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  } = useFormSecurity();

  // UTM 追蹤參數
  const [utmParams, setUtmParams] = useState({
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_term: '',
    utm_content: '',
    fbclid: ''
  });

  // 在頁面載入時抓取 UTM 參數
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const extractedUtmParams = {
        utm_source: urlParams.get('utm_source') || '',
        utm_medium: urlParams.get('utm_medium') || '',
        utm_campaign: urlParams.get('utm_campaign') || '',
        utm_term: urlParams.get('utm_term') || '',
        utm_content: urlParams.get('utm_content') || '',
        fbclid: urlParams.get('fbclid') || ''
      };

      setUtmParams(extractedUtmParams);
    }
  }, []);

  // 表單驗證函數
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // 姓名驗證
    if (!formData.name.trim()) {
      newErrors.name = '請填寫您的姓名';
    }

    // Email 必填驗證
    if (!formData.email.trim()) {
      newErrors.email = '請填寫 Email 地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email 格式不正確';
    }

    // 電話必填驗證
    if (!formData.phone.trim()) {
      newErrors.phone = '請填寫聯絡電話';
    } else {
      const cleanPhone = formData.phone.replace(/\D/g, '');
      // 支援 09xxxxxxxx (10位) 或 886xxxxxxxxx (12位)
      if (!/^(09\d{8}|886\d{9,10})$/.test(cleanPhone)) {
        newErrors.phone = '電話格式不正確（請輸入09開頭或+886開頭的電話號碼）';
      }
    }

    // 諮詢主題驗證
    if (!formData.topic) {
      newErrors.topic = '請選擇諮詢主題';
    }

    // 問題或需求驗證
    if (!formData.message.trim()) {
      newErrors.message = '請填寫您的問題或需求';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 即時驗證函數
  const validateField = (fieldName: string, value: string) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'name':
        if (!value.trim()) {
          newErrors.name = '請填寫您的姓名';
        } else {
          delete newErrors.name;
        }
        break;
      case 'email':
        if (!value.trim()) {
          newErrors.email = '請填寫 Email 地址';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          newErrors.email = 'Email 格式不正確';
        } else {
          delete newErrors.email;
        }
        break;
      case 'phone':
        if (!value.trim()) {
          newErrors.phone = '請填寫聯絡電話';
        } else {
          const cleanPhone = value.replace(/\D/g, '');
          if (!/^(09\d{8}|886\d{9,10})$/.test(cleanPhone)) {
            newErrors.phone = '電話格式不正確（請輸入09開頭或+886開頭的電話號碼）';
          } else {
            delete newErrors.phone;
          }
        }
        break;
      case 'topic':
        if (!value) {
          newErrors.topic = '請選擇諮詢主題';
        } else {
          delete newErrors.topic;
        }
        break;
      case 'message':
        if (!value.trim()) {
          newErrors.message = '請填寫您的問題或需求';
        } else {
          delete newErrors.message;
        }
        break;
    }

    setErrors(newErrors);
  };

  // 處理表單欄位變更
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    validateField(field, value);
  };



  // 檢查表單是否有效
  useEffect(() => {
    const hasRequiredFields =
      formData.name.trim() &&
      formData.topic &&
      formData.message.trim() &&
      // Email 和電話都必填
      formData.email.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.phone.trim() &&
      /^(09\d{8}|886\d{9,10})$/.test(formData.phone.replace(/\D/g, ''));

    setIsFormValid(Boolean(hasRequiredFields));
  }, [formData]);

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面頂部內容 (全寬) */}
      <section className="bg-white py-12 md:py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            {isIOSSafari ? (
              <div>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] leading-tight mb-6">
                  預約您的專家諮詢
                </h1>
                <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                  無論您是對我們的服務有任何疑問、尋求合作機會，或需要客製化的收藏建議，都歡迎您填寫表單。我們的初步諮詢完全免費且無任何購買壓力，旨在深入了解您的需求，並提供最有價值的解決方案。
                </p>
              </div>
            ) : (
              <ScrollTriggerMotion variants={titleScrollVariants}>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] leading-tight mb-6">
                  預約您的專家諮詢
                </h1>
                <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                  無論您是對我們的服務有任何疑問、尋求合作機會，或需要客製化的收藏建議，都歡迎您填寫表單。我們的初步諮詢完全免費且無任何購買壓力，旨在深入了解您的需求，並提供最有價值的解決方案。
                </p>
              </ScrollTriggerMotion>
            )}
          </div>
        </div>
      </section>

      {/* 雙欄式佈局 */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">

            {/* 左側主欄：諮詢表單 (佔 2/3 寬度) */}
            <div className="lg:col-span-2">
              <Card className="shadow-sm border border-gray-200">
                <CardContent className="p-6 md:p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">

                    {/* 安全防護欄位 - 對使用者隱藏 */}
                    <HoneypotField
                      value={honeypotValue}
                      onChange={setHoneypotValue}
                    />
                    <TimestampField startTime={formStartTime} />
                    <SecurityTokenField token={securityToken} />

                    {/* 姓名欄位 */}
                    <div>
                      <Label htmlFor="name" className="text-sm font-medium text-[#2b354d]">
                        您的姓名 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="請輸入您的姓名或暱稱"
                        className={`mt-2 ${errors.name ? 'border-red-500' : ''}`}
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    {/* Email 欄位 */}
                    <div>
                      <Label htmlFor="email" className="text-sm font-medium text-[#2b354d]">
                        Email 地址 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        type="email"
                        id="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="請輸入您的 Email 地址"
                        className={`mt-2 ${errors.email ? 'border-red-500' : ''}`}
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                      )}
                    </div>

                    {/* 聯絡電話欄位 */}
                    <div>
                      <Label htmlFor="phone" className="text-sm font-medium text-[#2b354d]">
                        聯絡電話 <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        type="tel"
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="請輸入您的聯絡電話"
                        className={`mt-2 ${errors.phone ? 'border-red-500' : ''}`}
                      />
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
                      )}
                    </div>

                    {/* 諮詢主題欄位 */}
                    <div>
                      <Label htmlFor="topic" className="text-sm font-medium text-[#2b354d]">
                        諮詢主題 <span className="text-red-500">*</span>
                      </Label>
                      <Select value={formData.topic} onValueChange={(value) => handleInputChange('topic', value)}>
                        <SelectTrigger className={`mt-2 ${errors.topic ? 'border-red-500' : ''}`}>
                          <SelectValue placeholder="請選擇諮詢主題" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Pangea 智慧收藏盒">Pangea 智慧收藏盒</SelectItem>
                          <SelectItem value="典藏錶款服務">典藏錶款服務</SelectItem>
                          <SelectItem value="錶匠體驗活動">錶匠體驗活動</SelectItem>
                          <SelectItem value="商業與媒體合作">商業與媒體合作</SelectItem>
                          <SelectItem value="其他問題">其他問題</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.topic && (
                        <p className="mt-1 text-sm text-red-500">{errors.topic}</p>
                      )}
                    </div>

                    {/* 問題或需求欄位 */}
                    <div>
                      <Label htmlFor="message" className="text-sm font-medium text-[#2b354d]">
                        您的問題或需求 <span className="text-red-500">*</span>
                      </Label>
                      <Textarea
                        id="message"
                        rows={5}
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        placeholder="請在此詳細說明您的需求，讓我們能為您做最好的準備。"
                        className={`mt-2 resize-vertical ${errors.message ? 'border-red-500' : ''}`}
                      />
                      {errors.message && (
                        <p className="mt-1 text-sm text-red-500">{errors.message}</p>
                      )}
                    </div>

                    {/* 提交按鈕 */}
                    <div className="pt-4">
                      <Button
                        type="submit"
                        disabled={!isFormValid || isSubmitting}
                        className={`w-full py-4 ${
                          isFormValid && !isSubmitting
                            ? 'bg-[#2b354d] hover:bg-[#1e2a3a] cursor-pointer'
                            : 'bg-gray-400 cursor-not-allowed'
                        }`}
                      >
                        {isSubmitting ? (
                          <div className="flex items-center justify-center">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                            送出中...
                          </div>
                        ) : (
                          <div className="flex items-center justify-center">
                            提交諮詢預約
                            <ArrowRight className="ml-2 h-5 w-5" />
                          </div>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* 右側副欄：快速引導連結 (佔 1/3 寬度) */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-[#2b354d] mb-4 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2 text-[#f59e0b]" />
                  正在尋找特定資訊？
                </h3>

                <p className="text-sm text-gray-600 mb-6">
                  您的問題，或許在這裡能更快找到答案：
                </p>

                <div className="space-y-4">
                  {/* 引導至 Pangea 預約頁 */}
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <Link
                      href="/pangea-booking"
                      className="block group"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-[#2b354d] group-hover:text-[#f59e0b] transition-colors flex items-center">
                            預約實際體驗 Pangea
                            <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            (直接前往錶盒線下鑑賞預約單)
                          </p>
                        </div>

                      </div>
                    </Link>
                  </div>

                  {/* 引導至支援中心 */}
                  <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <Link
                      href="/support/pangea-faq"
                      className="block group"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-[#2b354d] group-hover:text-[#f59e0b] transition-colors flex items-center">
                            查看 Pangea 使用手冊
                            <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            (包含操作說明與常見問題)
                          </p>
                        </div>

                      </div>
                    </Link>
                  </div>
                </div>

                {/* 聯絡資訊 */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-medium text-[#2b354d] mb-3">其他聯絡方式</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <p className="flex items-center">
                      <Mail className="w-4 h-4 mr-2 text-[#f59e0b]" />
                      <EMAIL>
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      我們會在 48 小時內回覆您的訊息
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );

  // 表單提交處理
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // 安全驗證：檢查蜜罐欄位和提交時間
    const securityValidation = validateBeforeSubmit();
    if (!securityValidation.isValid) {
      alert(securityValidation.reason || '表單驗證失敗，請重新整理頁面後再試');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          submittedAt: (() => {
            const now = new Date();
            const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
            return utc8Time.toISOString().replace('Z', '+08:00');
          })(),
          // 添加 UTM 參數
          utmParams,
          // 添加安全驗證資料
          security: {
            honeypotValue,
            formStartTime,
            securityToken,
            submissionTime: Date.now(),
            userAgent: navigator.userAgent,
          },
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert('感謝您的聯絡！我們會在 48 小時內回覆您。');
        // 重置表單
        setFormData({
          name: '',
          email: '',
          phone: '',
          contactMethod: 'email',
          topic: '',
          message: '',
        });
        setErrors({});
        // 重置安全狀態
        resetSecurity();
      } else {
        throw new Error(result.error || '提交失敗');
      }
    } catch (error) {
      console.error('提交表單時發生錯誤:', error);
      alert('提交失敗，請稍後再試');
    } finally {
      setIsSubmitting(false);
    }
  }
}
