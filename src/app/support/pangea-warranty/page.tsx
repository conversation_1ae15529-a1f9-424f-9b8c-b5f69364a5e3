'use client';

import Link from 'next/link';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants } from '@/lib/motion-config';
import { useIsIOSSafari } from '@/hooks/useBrowserDetection';

export default function PangeaWarrantyPage() {
  const { isIOSSafari } = useIsIOSSafari();
  return (
    <main className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            {isIOSSafari ? (
              <div>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] mb-6">
                  Pangea 保固條款
                </h1>
                <p className="text-base md:text-lg text-gray-600">
                  為確保您的權益，請詳閱以下保固條款與服務說明
                </p>
              </div>
            ) : (
              <ScrollTriggerMotion variants={titleScrollVariants}>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] mb-6">
                  Pangea 保固條款
                </h1>
                <p className="text-base md:text-lg text-gray-600">
                  為確保您的權益，請詳閱以下保固條款與服務說明
                </p>
              </ScrollTriggerMotion>
            )}
          </div>
        </div>
      </section>

      {/* 保固內容 */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-xl md:text-2xl font-bold text-[#2b354d] mb-6">保固政策說明</h2>

            <div className="prose prose-gray max-w-none">
                <p className="text-lg font-semibold mb-0">
                  產品皆有兩年的保固，以說明書上的日期為主
                </p>

              <p className="text-gray-700 mb-6">
                感謝您選購本公司產品，為確保您的權益，請詳閱以下事項：
              </p>

              <ul className="space-y-4 text-gray-700">
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  產品提供自購買日起兩年內享有免費保固。在正常操作使用下發生故障，本公司將提供免費維修服務。保固期限與保固方式及其範圍，以保固證明中詳細內容為準
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  保固服務不包括運輸費用，故若有保固維修時，客戶須自行負擔寄回費用，我司承擔寄出費用
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  產品檢驗後若發現是產品本身設計缺陷，我們將盡最大努力提供免費維修、或以相同或等值產品進行更換
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  超過保固期限，需更換零組件時，將酌收所需之零組件費用，維修使用之零組件部分將享有修復日起 90 天之保固期限。對於零組件來料短缺、已停產或無法再受理付費維修時，本公司將保有最終決定權和核審權
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  當送修之產品超過保固期間，本公司仍將提供免費檢測服務。如有故障需付費維修，買方可自行決定是否維修，若買方放棄維修則送修商品之寄返運費由買方支付。本公司將不負該送修品之保管與運送責任
                </li>
                <li className="flex items-start">
                  <span className="inline-block w-2 h-2 bg-[#f59e0b] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  產品送修限台灣地區包含澎、金、馬等地區，其餘地區均不在受理範圍
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 保固排除條款 */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8">
            <h2 className="text-xl md:text-2xl font-bold text-red-800 mb-6">兩年保固政策不涵蓋以下：</h2>

            <ul className="space-y-3 text-red-700">
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                商品無保固證明、訂購單號或序號與實物不符合者
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                本保固亦不適用於二手轉賣之產品
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                因正常磨損而導致之損壞及/或瑕疵，例如表面問題塗層剝落及褪色
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                因暴露於極端溫度、溶劑、酸、水、火等造成的任何損壞
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                天然災害、人為災害及環境因素等造成的故障、傷害、損壞。如地震、雷擊、火災、異常電壓等
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                未按使用說明書規定操作，或因人為不當使用、拆裝造成的故障、傷害、損壞
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                使用環境不符合使用說明書中規定
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                標籤貼紙、序號貼紙有毀損、移除或塗改、置換、變造之痕跡
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                機體序號與保固證明序號不符
              </li>
              <li className="flex items-start">
                <span className="text-red-500 mr-2 flex-shrink-0">•</span>
                機殼 / 機體經自行或未經本公司授權之廠商、技術人員拆卸、移除、置換等操作
              </li>
            </ul>

            <p className="text-red-600 text-sm mt-6 font-medium">
              ※上述產品保固說明，為烽保留以書面或網站解釋之權利
            </p>
          </div>
        </div>
      </section>

      {/* 聯絡資訊 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-xl md:text-2xl font-bold text-[#2b354d] mb-6">
            需要保固服務？
          </h2>
          <p className="text-gray-600 mb-8">
            如需申請保固服務或有任何疑問，歡迎與我們聯繫
          </p>
          <Link
            href="/contact"
            className="inline-flex items-center px-8 py-3 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            聯絡客服
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </section>
    </main>
  );
}
