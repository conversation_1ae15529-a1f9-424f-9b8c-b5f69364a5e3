'use client';

import { useState, useEffect } from 'react';
import { FAQ } from '@/types/faq';

interface FaqItemProps {
  faq: FAQ;
  isOpen: boolean;
  onClick: () => void;
}

// 處理FAQ答案中的列表格式
const formatFaqAnswer = (answer: string): string => {
  // 將 "- " 開頭的行轉換為HTML列表
  const lines = answer.split('\n');
  let formattedAnswer = '';
  let inList = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.startsWith('- ')) {
      // 如果還沒開始列表，開始一個新的ul
      if (!inList) {
        formattedAnswer += '<ul>';
        inList = true;
      }
      // 移除 "- " 並包裝在li標籤中
      const listItem = line.substring(2).trim();
      formattedAnswer += `<li>${listItem}</li>`;
    } else {
      // 如果之前在列表中，結束列表
      if (inList) {
        formattedAnswer += '</ul>';
        inList = false;
      }
      // 添加普通段落（如果不是空行）
      if (line) {
        formattedAnswer += `<p>${line}</p>`;
      }
    }
  }

  // 如果最後還在列表中，結束列表
  if (inList) {
    formattedAnswer += '</ul>';
  }

  return formattedAnswer;
};

const FaqItem = ({ faq, isOpen, onClick }: FaqItemProps) => {
  return (
    <div className="border-b border-slate-200 mb-4">
      <button
        onClick={onClick}
        className="w-full text-left p-6 flex justify-between items-center focus:outline-none hover:bg-slate-50 transition-colors"
      >
        <span className="text-lg font-bold text-slate-900 pr-4">{faq.question}</span>
        <div className="flex-shrink-0">
          <svg
            className={`w-5 h-5 text-slate-500 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>
      {isOpen && (
        <div className="px-6 pb-6">
          <div
            className="text-slate-600 leading-relaxed pt-4 faq-content"
            dangerouslySetInnerHTML={{ __html: formatFaqAnswer(faq.answer) }}
          />
        </div>
      )}
    </div>
  );
};

const PangeaFaqPage = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<FAQ[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 獲取常見問題資料
  useEffect(() => {
    const fetchFAQs = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/support');
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '獲取常見問題失敗');
        }

        setFaqs(data.faqs || []);
        setTags(data.tags || []);
        setFilteredFaqs(data.faqs || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : '發生未知錯誤');
      } finally {
        setLoading(false);
      }
    };

    fetchFAQs();
  }, []);

  // 篩選邏輯
  useEffect(() => {
    let filtered = faqs;

    // 根據標籤篩選
    if (selectedTag !== 'all') {
      filtered = filtered.filter(faq =>
        faq.tags.some(tag => tag.toLowerCase() === selectedTag.toLowerCase())
      );
    }

    // 根據搜尋關鍵字篩選
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      );
    }

    setFilteredFaqs(filtered);
    setOpenIndex(null); // 重置展開狀態
  }, [faqs, selectedTag, searchQuery]);

  const handleTagChange = (tag: string) => {
    setSelectedTag(tag);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleFaqClick = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-900 mx-auto mb-4"></div>
          <p className="text-slate-600">載入中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">載入失敗：{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors"
          >
            重新載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 頁面標題 */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-slate-900 mb-4">Pangea 使用手冊</h1>
            <p className="text-lg text-slate-600">嗨，我們能幫上什麼忙嗎？</p>
          </div>
        </div>
      </div>

      {/* 問題篩選器 */}
      <section className="py-8 bg-white border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-2 mb-6">
            <button
              onClick={() => handleTagChange('all')}
              className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                selectedTag === 'all'
                  ? 'shadow-lg'
                  : 'bg-slate-100 hover:bg-slate-200'
              }`}
              style={{
                backgroundColor: selectedTag === 'all' ? '#2b354d' : undefined,
                color: selectedTag === 'all' ? '#ffffff' : '#2b354d'
              }}
            >
              全部
            </button>
            {tags.map((tag) => (
              <button
                key={tag}
                onClick={() => handleTagChange(tag)}
                className={`px-4 py-2 rounded-full text-xs font-medium transition-all duration-200 ${
                  selectedTag === tag
                    ? 'shadow-lg'
                    : 'bg-slate-100 hover:bg-slate-200'
                }`}
                style={{
                  backgroundColor: selectedTag === tag ? '#2b354d' : undefined,
                  color: selectedTag === tag ? '#ffffff' : '#2b354d'
                }}
              >
                {tag}
              </button>
            ))}
          </div>

          {/* 搜尋框 */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="搜尋問題..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full px-6 py-3 text-base border border-slate-300 rounded-full focus:outline-none focus:ring-2 focus:ring-slate-900 focus:border-transparent"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 常見問題列表 */}
      <div className="py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredFaqs.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-slate-600">
                {searchQuery || selectedTag !== 'all' ? '沒有找到符合條件的問題' : '目前沒有常見問題'}
              </p>
            </div>
          ) : (
            <div className="space-y-0">
              {filteredFaqs.map((faq, index) => (
                <FaqItem
                  key={faq.id}
                  faq={faq}
                  isOpen={openIndex === index}
                  onClick={() => handleFaqClick(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 聯絡資訊 */}
      <div className="border-t border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-slate-900 mb-4">
              無法解決問題？
            </h2>
            <p className="text-slate-600 mb-6">
              如果有任何其他問題，請聯絡{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                <EMAIL>
              </a>
              ，將由專人為您解答！
            </p>
          </div>
        </div>
      </div>

      {/* 全域 CSS 樣式 */}
      <style jsx global>{`
        .faq-content img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 16px 0;
        }

        .faq-content .videobox {
          position: relative;
          width: 100%;
          height: 0;
          padding-bottom: 56.25%; /* 16:9 長寬比 */
          margin: 16px 0;
          border-radius: 8px;
          overflow: hidden;
        }

        .faq-content .videobox iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border: none;
        }

        .faq-content iframe:not(.videobox iframe) {
          max-width: 100%;
          height: auto;
          aspect-ratio: 16/9;
          border-radius: 8px;
          margin: 16px 0;
        }

        .faq-content a {
          color: #2563eb;
          text-decoration: underline;
        }

        .faq-content a:hover {
          color: #1d4ed8;
        }

        .faq-content strong,
        .faq-content b {
          font-weight: 600;
          color: #1f2937;
        }

        /* 列表樣式 */
        .faq-content ul,
        .faq-content ol {
          margin: 16px 0;
          padding-left: 24px;
          list-style-position: outside;
        }

        .faq-content ul {
          list-style-type: disc;
        }

        .faq-content ol {
          list-style-type: decimal;
        }

        .faq-content li {
          margin: 8px 0;
          padding-left: 4px;
          display: list-item;
          list-style-position: outside;
        }

        /* 確保在 ul/ol 中的 li 標籤正確顯示 */
        .faq-content ul li {
          list-style-type: disc;
          display: list-item;
        }

        .faq-content ol li {
          list-style-type: decimal;
          display: list-item;
        }

        /* 處理沒有包在 ul/ol 中的獨立 li 標籤 */
        .faq-content > li:not(ul li):not(ol li) {
          position: relative;
          margin-left: 20px;
          margin-top: 8px;
          margin-bottom: 8px;
          display: list-item;
          list-style-type: disc;
          list-style-position: outside;
        }

        /* 嵌套列表的樣式 */
        .faq-content ul ul {
          list-style-type: circle;
          margin: 8px 0;
        }

        .faq-content ul ul ul {
          list-style-type: square;
        }

        .faq-content ol ol {
          list-style-type: lower-alpha;
        }

        .faq-content ol ol ol {
          list-style-type: lower-roman;
        }

        .faq-content h3,
        .faq-content h4 {
          font-weight: 600;
          color: #1f2937;
          margin: 20px 0 12px 0;
        }

        .faq-content h3 {
          font-size: 1.25rem;
        }

        .faq-content h4 {
          font-size: 1.125rem;
        }

        .faq-content p {
          margin: 12px 0;
        }

        .faq-content br {
          line-height: 1.5;
        }

        /* 確保所有響應式元素在小螢幕上正確顯示 */
        @media (max-width: 768px) {
          .faq-content img,
          .faq-content .videobox,
          .faq-content iframe:not(.videobox iframe) {
            margin: 12px 0;
          }
        }
      `}</style>
    </div>
  );
};

export default PangeaFaqPage;
