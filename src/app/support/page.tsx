'use client';

import Link from 'next/link';
import { FileText, Shield, Award } from 'lucide-react';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants } from '@/lib/motion-config';
import { useIsIOSSafari } from '@/hooks/useBrowserDetection';

export default function SupportPage() {
  const { isIOSSafari } = useIsIOSSafari();
  const supportLinks = [
    {
      title: 'Pangea 使用手冊',
      description: '詳細的操作指南與常見問題解答',
      href: '/support/pangea-faq',
      icon: FileText,
      color: '#f59e0b'
    },
    {
      title: 'Pangea 保固條款',
      description: 'Pangea 產品保固政策與條款說明',
      href: '/support/pangea-warranty',
      icon: Shield,
      color: '#f59e0b'
    },
    {
      title: '典藏錶款保固政策',
      description: '精選錶款的保固服務與政策',
      href: '/support/collection-warranty',
      icon: Award,
      color: '#f59e0b'
    }
  ];

  return (
    <main className="min-h-screen bg-white">
      {/* 頁面標題區塊 */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            {isIOSSafari ? (
              <div>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] leading-tight mb-6">
                  顧客支援
                </h1>
                <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                  我們為您提供完整的產品資訊與保固服務，讓您安心享受每一刻的精緻時光。
                </p>
              </div>
            ) : (
              <ScrollTriggerMotion variants={titleScrollVariants}>
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-[#2b354d] leading-tight mb-6">
                  顧客支援
                </h1>
                <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                  我們為您提供完整的產品資訊與保固服務，讓您安心享受每一刻的精緻時光。
                </p>
              </ScrollTriggerMotion>
            )}
          </div>
        </div>
      </section>

      {/* 支援卡片區塊 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {supportLinks.map((link, index) => {
              const IconComponent = link.icon;
              return (
                <Link
                  key={index}
                  href={link.href}
                  className="group block"
                >
                  <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-105 p-8 h-full">
                    <div className="text-center">
                      {/* 圖標 */}
                      <div className="mb-6">
                        <IconComponent 
                          className="w-12 h-12 mx-auto"
                          style={{ color: link.color }}
                        />
                      </div>
                      
                      {/* 標題 */}
                      <h3 className="text-xl font-bold text-[#2b354d] mb-4 group-hover:text-[#f59e0b] transition-colors">
                        {link.title}
                      </h3>
                      
                      {/* 描述 */}
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {link.description}
                      </p>
                      
                      {/* 箭頭指示 */}
                      <div className="mt-6">
                        <span className="inline-flex items-center text-sm font-medium text-[#2b354d] group-hover:text-[#f59e0b] transition-colors">
                          查看詳情
                          <svg 
                            className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* 額外聯絡資訊 */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-xl md:text-2xl font-bold text-[#2b354d] mb-6">
            需要更多協助？
          </h2>
          <p className="text-gray-600 mb-8">
            如果您在上述資源中找不到所需的資訊，歡迎直接與我們聯繫。
          </p>
          <Link
            href="/contact"
            className="inline-flex items-center px-8 py-3 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            聯絡我們
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </section>
    </main>
  );
}
