import { Metadata } from 'next';
import { Suspense } from 'react';
import { getServerBlogPostEdge } from '@/lib/blog-server-edge';
import { generateDynamicMetadata } from '@/lib/seo-utils';
import BlogDetailContentEdge from '@/components/BlogDetailContentEdge';
import BlogDetailSkeleton from '@/components/BlogDetailSkeleton';

// 移除靜態路徑生成，因為使用 Google Sheets 動態資料
// export function generateStaticParams() {
//   return generateBlogStaticParams();
// }

// 生成 metadata
export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  const post = await getServerBlogPostEdge(resolvedParams.slug);

  if (!post) {
    return {
      title: '文章不存在',
      description: '找不到指定的文章',
    };
  }

  // 使用簡化的動態 metadata 生成
  const title = post.seoTitle || `${post.title} - Weaven Blog`;
  const description = post.seoDescription || post.title;
  const image = post.socialImage || post.thumbnail;

  return generateDynamicMetadata(title, description, image, `/blog/${post.slug}`);
}

const BlogPostPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const resolvedParams = await params;

  return (
    <Suspense fallback={<BlogDetailSkeleton />}>
      <BlogDetailContentEdge slug={resolvedParams.slug} />
    </Suspense>
  );
};

export default BlogPostPage;
