'use client';

import Image from 'next/image';
import Link from 'next/link';
import { ArrowRight, Heart, Users, Target } from 'lucide-react';
import { ScrollTriggerMotion } from '@/components/motion/MotionWrapper';
import { titleScrollVariants } from '@/lib/motion-config';
import { useIsIOSSafari } from '@/hooks/useBrowserDetection';

export default function AboutPage() {
  const { isIOSSafari } = useIsIOSSafari();
  return (
    <div className="min-h-screen bg-white">
      {/* 1. 頁面頂部主視覺 (Hero Image) */}
      <section className="relative h-[60vh] min-h-[400px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/images/about-hero.jpg"
            alt="關於 Weaven - 品牌故事"
            fill
            className="object-cover"
            sizes="100vw"
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {isIOSSafari ? (
            <div>
              <h1 className="text-3xl md:text-5xl font-bold text-white leading-tight mb-6">
                關於我們
              </h1>
              <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
                始於熱愛，專於服務的腕錶專業團隊
              </p>
            </div>
          ) : (
            <ScrollTriggerMotion variants={titleScrollVariants}>
              <h1 className="text-3xl md:text-5xl font-bold text-white leading-tight mb-6">
                關於我們
              </h1>
              <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto leading-relaxed">
                始於熱愛，專於服務的腕錶專業團隊
              </p>
            </ScrollTriggerMotion>
          )}
        </div>
      </section>

      {/* 2. 第一區塊：品牌理念 */}
      <section className="py-16 md:py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <Heart className="w-8 h-8 text-[#f59e0b] mr-3" />
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d]">
                我們的理念：始於熱愛，專於服務
              </h2>
            </div>
          </div>

          <div className="prose prose-lg mx-auto text-gray-700 leading-relaxed">
            <p className="text-base md:text-lg mb-6">
              我們相信，每一支機械腕錶都不僅僅是時間的載體，它承載著工藝的傳承、個人的故事，以及對極致美學的追求。然而，在腕錶的世界裡，從收藏、鑑賞、保養到交流，每一個環節的體驗往往是破碎而分散的。
            </p>

            <p className="text-base md:text-lg mb-6">
              Weaven 的誕生，源於一個單純的初衷：為所有和我們一樣的腕錶愛好者，打造一個值得信賴、全方位的專業平台。我們致力於整合所有資源，將複雜的過程變得簡單、透明，讓您可以更純粹地享受腕錶帶來的樂趣。
            </p>

            <div className="bg-[#f8fafc] p-6 rounded-lg border-l-4 border-[#f59e0b] my-8">
              <p className="text-base md:text-lg font-semibold text-[#2b354d] mb-0">
                <strong>Weaven 的使命，是成為每一位腕錶愛好者最可靠的夥伴，陪伴您走過腕錶旅程的每一步，讓享受機械錶變簡單。</strong>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 3. 第二區塊：團隊與專業 */}
      <section className="py-16 md:py-20 bg-[#2b354d]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <Users className="w-8 h-8 text-[#f59e0b] mr-3" />
              <h2 className="text-2xl md:text-4xl font-bold text-white">
                專業的背後，是熱情的靈魂
              </h2>
            </div>
          </div>

          <div className="prose prose-lg mx-auto leading-relaxed">
            <p className="text-base md:text-lg mb-6 text-white">
              我們的團隊由一群資深的腕錶藏家、經認證的製錶師傅，以及對客戶服務充滿熱忱的專家所組成。我們不只是銷售產品或服務，我們和您一樣，會在深夜為一枚完美的藍鋼指針而讚嘆，會為一個機芯的打磨細節而著迷。
            </p>

            <p className="text-base md:text-lg mb-6 text-white">
              正是這份共同的熱愛，驅使我們用最嚴苛的標準來要求自己。從 Pangea 智慧收藏盒的研發，到每一支典藏錶款的檢測與認證，我們將專業知識貫徹在每一個細節之中，確保您在 Weaven 得到的，永遠是最好的品質與最真誠的建議。
            </p>
          </div>

          {/* 預留團隊照片位置 */}
          <div className="mt-12 text-center">
            <div className="bg-white/10 rounded-lg p-8 border border-white/20">
              <p className="text-white/70 italic">
                團隊或創辦人形象照預留位置
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 4. 第三區塊：行動呼籲 (Call to Action) */}
      <section className="py-16 md:py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-6">
              <Target className="w-8 h-8 text-[#f59e0b] mr-3" />
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d]">
                展開您的腕錶旅程
              </h2>
            </div>
            <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              無論您是剛踏入機械腕錶世界的新朋友，還是經驗豐富的資深藏家，Weaven 都希望能成為您旅程中的一部分。我們邀請您繼續探索，發現更多可能。
            </p>
          </div>

          {/* CTA 按鈕列表 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* CTA 1: 預約專家諮詢 */}
            <div className="text-center p-6 bg-[#f8fafc] rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300">
              <p className="text-gray-700 mb-4 font-medium">
                需要個人化的建議嗎？
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center px-6 py-3 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
              >
                預約專家諮詢
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>

            {/* CTA 2: 探索典藏錶款 */}
            <div className="text-center p-6 bg-[#f8fafc] rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300">
              <p className="text-gray-700 mb-4 font-medium">
                準備好尋找您的下一支收藏了嗎？
              </p>
              <Link
                href="/pre-owned-watches"
                className="inline-flex items-center px-6 py-3 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
              >
                探索我們的典藏錶款
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>

            {/* CTA 3: 閱讀文章 */}
            <div className="text-center p-6 bg-[#f8fafc] rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300">
              <p className="text-gray-700 mb-4 font-medium">
                想了解更多腕錶知識？
              </p>
              <Link
                href="/blog"
                className="inline-flex items-center px-6 py-3 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 group"
              >
                閱讀最新腕錶文章
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
