/**
 * 首頁組件測試
 * 測試首頁的渲染和基本互動功能
 *
 * 注意：首頁內容正在重構中，暫時清空測試內容
 * TODO: 重構完成後需要重新編寫測試
 */

import React from 'react';
import { render } from '@testing-library/react';
import { jest } from '@jest/globals';
import HomePage from '@/app/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock motion components
jest.mock('@/components/motion/MotionWrapper', () => ({
  ScrollTriggerMotion: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  CardMotion: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock browser detection hook
jest.mock('@/hooks/useBrowserDetection', () => ({
  useIsIOSSafari: () => false,
}));

// Mock LazyImage component
jest.mock('@/components/LazyImage', () => {
  return function MockLazyImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock API calls
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(global as any).fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ posts: [], total: 0, hasMore: false }),
  })
);

describe('HomePage', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染首頁', () => {
      const { container } = render(<HomePage />);

      // 基本渲染測試 - 檢查頁面是否能正常載入
      // TODO: 重構完成後添加具體的內容測試
      expect(container).toBeTruthy();
    });
  });

});
