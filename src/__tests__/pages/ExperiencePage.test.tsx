/**
 * 錶匠體驗報名頁面組件測試 - 簡化版
 * 專注於功能測試，避免依賴具體文字內容
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest, test, describe, expect, beforeEach } from '@jest/globals';
import ExperiencePage from '@/app/experience/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock useFormSecurity hook
const mockValidateBeforeSubmit = jest.fn(() => ({ isValid: true }));
jest.mock('@/hooks/useFormSecurity', () => ({
  useFormSecurity: () => ({
    honeypotValue: '',
    formStartTime: Date.now() - 10000,
    securityToken: 'test_token_123',
    isReady: true,
    setHoneypotValue: jest.fn(),
    validateBeforeSubmit: mockValidateBeforeSubmit,
    resetSecurity: jest.fn(),
  }),
}));

// Mock fetch
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

describe('ExperiencePage - 核心功能測試', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock scrollIntoView for JSDOM environment
    Element.prototype.scrollIntoView = jest.fn();

    // Mock getElementById to ensure it returns elements with scrollIntoView
    const originalGetElementById = document.getElementById;
    document.getElementById = jest.fn((id: string) => {
      const element = originalGetElementById.call(document, id);
      if (element && !element.scrollIntoView) {
        element.scrollIntoView = jest.fn();
      }
      return element;
    });

    (global.fetch as jest.Mock).mockImplementation((url: unknown) => {
      if (url === '/api/event-registration') {
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true, orderNo: 'pangea_test_123' })
        });
      }

      return Promise.resolve({
        ok: true,
        json: async () => ({ success: true })
      });
    });
  });

  describe('基本渲染測試', () => {
    test('應該渲染主要頁面元素', () => {
      render(<ExperiencePage />);

      expect(screen.getAllByText('立即報名').length).toBeGreaterThan(0);
      expect(screen.getByRole('button', { name: /提交報名/ })).toBeInTheDocument();
      expect(screen.getByRole('checkbox', { name: /我同意/ })).toBeInTheDocument();
    });

    test('應該顯示表單欄位', async () => {
      render(<ExperiencePage />);

      const signUpButtons = screen.getAllByText('立即報名');
      await user.click(signUpButtons[0]);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('王大明')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('0912345678')).toBeInTheDocument();
        expect(screen.getByTestId('age-select')).toBeInTheDocument();
        expect(screen.getByTestId('region-select')).toBeInTheDocument();
      });
    });

    test('提交按鈕初始為禁用狀態', () => {
      render(<ExperiencePage />);

      const submitButton = screen.getByRole('button', { name: /提交報名/ });
      expect(submitButton).toBeDisabled();
    });
  });

  describe('表單驗證測試', () => {
    test('應該驗證 email 格式', async () => {
      render(<ExperiencePage />);

      const signUpButtons = screen.getAllByText('立即報名');
      await user.click(signUpButtons[0]);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
      });

      const emailInput = screen.getByPlaceholderText('<EMAIL>');
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      const submitButton = screen.getByRole('button', { name: /提交報名/ });

      await user.type(emailInput, 'invalid-email');
      await user.click(agreeCheckbox);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-email')).toBeInTheDocument();
      });
    });

    test('應該驗證電話號碼格式', async () => {
      render(<ExperiencePage />);

      const signUpButtons = screen.getAllByText('立即報名');
      await user.click(signUpButtons[0]);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('0912345678')).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText('0912345678');
      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      const submitButton = screen.getByRole('button', { name: /提交報名/ });

      await user.type(phoneInput, '123');
      await user.click(agreeCheckbox);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-phone')).toBeInTheDocument();
      });
    });
  });

  describe('表單互動測試', () => {
    test('應該能夠填寫基本資料', async () => {
      render(<ExperiencePage />);

      const signUpButtons = screen.getAllByText('立即報名');
      await user.click(signUpButtons[0]);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('王大明')).toBeInTheDocument();
      });

      const nameInput = screen.getByPlaceholderText('王大明') as HTMLInputElement;
      const emailInput = screen.getByPlaceholderText('<EMAIL>') as HTMLInputElement;
      const phoneInput = screen.getByPlaceholderText('0912345678') as HTMLInputElement;

      await user.type(nameInput, '張測試');
      await user.type(emailInput, '<EMAIL>');
      await user.type(phoneInput, '0912345678');

      expect(nameInput.value).toBe('張測試');
      expect(emailInput.value).toBe('<EMAIL>');
      expect(phoneInput.value).toBe('0912345678');
    });

    test('應該能夠選擇參與類型', async () => {
      render(<ExperiencePage />);

      const individualRadio = screen.getByRole('radio', { name: '個人報名' });
      const pairRadio = screen.getByRole('radio', { name: '雙人團報' });

      expect(individualRadio).not.toBeChecked();
      expect(pairRadio).not.toBeChecked();

      await user.click(individualRadio);
      expect(individualRadio).toBeChecked();
      expect(pairRadio).not.toBeChecked();

      await user.click(pairRadio);
      expect(pairRadio).toBeChecked();
      expect(individualRadio).not.toBeChecked();
    });

    test('應該能夠同意條款', async () => {
      render(<ExperiencePage />);

      const agreeCheckbox = screen.getByRole('checkbox', { name: /我同意/ });
      expect(agreeCheckbox).not.toBeChecked();

      await user.click(agreeCheckbox);
      expect(agreeCheckbox).toBeChecked();
    });
  });
});
