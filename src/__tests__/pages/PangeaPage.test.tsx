/**
 * Pangea 頁面組件測試
 * 測試 Pangea 頁面的渲染和基本互動功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import PangeaPage from '@/app/pangea/page';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: React.ComponentProps<'img'>) {
    return <img src={src} alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock AutoCarousel component
jest.mock('@/components/AutoCarousel', () => {
  return function MockAutoCarousel({ images }: { images: Array<{ src: string; alt: string }> }) {
    return (
      <div data-testid="auto-carousel">
        {images.map((image: { src: string; alt: string }, index: number) => (
          <img key={index} src={image.src} alt={image.alt} />
        ))}
      </div>
    );
  };
});

describe('PangeaPage', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('基本渲染測試', () => {
    test('應該正確渲染頁面主要元素', () => {
      render(<PangeaPage />);

      // 檢查頁面是否有主要內容區域
      expect(document.body).toContainHTML('PANGEA');

      // 檢查主要區塊
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });

    test('應該顯示主要 CTA 按鈕', () => {
      render(<PangeaPage />);

      // 檢查是否有連結到預約頁面的按鈕
      const bookingLinks = screen.getAllByRole('link');
      const pangeaBookingLink = bookingLinks.find(link =>
        link.getAttribute('href') === '/pangea-booking'
      );
      expect(pangeaBookingLink).toBeInTheDocument();
    });

    test('應該顯示主要內容區塊', () => {
      render(<PangeaPage />);

      // 檢查主要內容區域
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();

      // 檢查是否包含 PANGEA 相關內容
      expect(document.body).toContainHTML('PANGEA');
    });
  });

  describe('響應式設計測試', () => {
    test('應該在桌面版正確顯示', () => {
      // 模擬桌面螢幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });

      render(<PangeaPage />);

      // 檢查桌面版特有的元素
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });

    test('應該在行動版正確顯示', () => {
      // 模擬行動裝置螢幕尺寸
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<PangeaPage />);

      // 檢查行動版的響應式元素
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });
  });

  describe('互動功能測試', () => {
    test('應該正確處理 CTA 按鈕點擊', () => {
      render(<PangeaPage />);

      // 測試預約連結
      const bookingLinks = screen.getAllByRole('link');
      const pangeaBookingLink = bookingLinks.find(link =>
        link.getAttribute('href') === '/pangea-booking'
      );

      expect(pangeaBookingLink).toBeInTheDocument();
      fireEvent.click(pangeaBookingLink!);

      // 驗證連結正確
      expect(pangeaBookingLink).toHaveAttribute('href', '/pangea-booking');
    });

    test('應該正確處理滾動功能', async () => {
      // Mock scrollIntoView
      const mockScrollIntoView = jest.fn();
      Element.prototype.scrollIntoView = mockScrollIntoView;

      render(<PangeaPage />);

      // 檢查頁面渲染正常
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });
  });

  describe('內容區塊測試', () => {
    test('應該顯示主要內容區塊', () => {
      render(<PangeaPage />);

      // 檢查主要區塊
      const heroSection = screen.getByRole('main');
      expect(heroSection).toBeInTheDocument();

      // 檢查是否包含 PANGEA 相關內容
      expect(document.body).toContainHTML('PANGEA');
    });

    test('應該顯示圖片內容', () => {
      render(<PangeaPage />);

      // 檢查是否有圖片元素
      const images = screen.getAllByRole('img');
      expect(images.length).toBeGreaterThan(0);
    });

    test('應該顯示預約連結', () => {
      render(<PangeaPage />);

      // 檢查預約連結
      const bookingLinks = screen.getAllByRole('link');
      const pangeaBookingLink = bookingLinks.find(link =>
        link.getAttribute('href') === '/pangea-booking'
      );
      expect(pangeaBookingLink).toBeInTheDocument();
    });
  });

  describe('SEO 和可訪問性測試', () => {
    test('應該有正確的頁面標題', () => {
      render(<PangeaPage />);

      // 檢查主要標題的語義標籤 - 使用 getAllByRole 因為有多個 h1
      const mainHeadings = screen.getAllByRole('heading', { level: 1 });
      expect(mainHeadings.length).toBeGreaterThan(0);
    });

    test('應該有正確的圖片 alt 屬性', () => {
      render(<PangeaPage />);
      
      // 檢查所有圖片都有 alt 屬性
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('alt');
        expect(img.getAttribute('alt')).not.toBe('');
      });
    });

    test('應該有正確的連結可訪問性', () => {
      render(<PangeaPage />);
      
      // 檢查所有連結都有可訪問的文字
      const links = screen.getAllByRole('link');
      links.forEach(link => {
        expect(link).toHaveAccessibleName();
      });
    });

    test('應該有正確的按鈕可訪問性', () => {
      render(<PangeaPage />);
      
      // 檢查所有按鈕都有可訪問的文字
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAccessibleName();
      });
    });
  });

  describe('錯誤處理測試', () => {
    test('應該優雅處理圖片載入失敗', () => {
      render(<PangeaPage />);

      // 模擬圖片載入失敗
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        fireEvent.error(img);
      });

      // 頁面應該仍然可用
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
    });

    test('應該優雅處理組件載入失敗', () => {
      // 這個測試確保即使某些組件失敗，頁面仍然可用
      render(<PangeaPage />);

      // 檢查核心內容仍然存在
      const mainSection = screen.getByRole('main');
      expect(mainSection).toBeInTheDocument();
      expect(document.body).toContainHTML('PANGEA');
    });
  });
});
