/**
 * Retry Payment API 路由測試
 * 測試 /api/retry-payment 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/retry-payment/route';

// Mock dependencies
jest.mock('@/lib/google-sheets-edge', () => ({
  getSheetDataEdge: jest.fn(),
  updateSheetDataEdge: jest.fn(),
}));

jest.mock('@/lib/google-sheets', () => ({
  getSheetData: jest.fn(),
  updateSheetRow: jest.fn(),
  getSheetsClient: jest.fn(),
}));

jest.mock('@/lib/payuni-edge', () => ({
  createPaymentRequestEdge: jest.fn(),
  calculateATMExpireDate: jest.fn(),
}));

jest.mock('@/lib/payuni', () => ({
  createPaymentRequest: jest.fn(),
  calculateATMExpireDate: jest.fn(),
}));

jest.mock('@/config/environment-config', () => ({
  PAYUNI_CONFIG: {
    getMerchantId: jest.fn(() => 'S01421169'),
    getApiUrl: jest.fn(() => 'https://sandbox-api.payuni.com.tw/api/upp'),
    getHashKey: jest.fn(() => 'test_hash_key'),
    getHashIV: jest.fn(() => 'test_hash_iv'),
  },
  GOOGLE_SHEETS_CONFIG: {
    getSheetId: jest.fn(() => 'test-sheet-id'),
  },
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/retry-payment', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock environment variables
    process.env.APP_ENVIRONMENT = 'sandbox';
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';

    // Setup mock return values
    const { getSheetDataEdge, updateSheetDataEdge } = require('@/lib/google-sheets-edge');
    const { getSheetData, updateSheetRow, getSheetsClient } = require('@/lib/google-sheets');
    const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

    getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
    updateSheetDataEdge.mockResolvedValue({ success: true });
    getSheetData.mockResolvedValue(mockGoogleSheetsData);
    updateSheetRow.mockResolvedValue({ success: true });
    createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);
    calculateATMExpireDate.mockReturnValue('2025-07-12');

    // Mock Google Sheets client
    const mockSheetsClient = {
      spreadsheets: {
        values: {
          update: jest.fn().mockResolvedValue({ status: 200 })
        }
      }
    };
    getSheetsClient.mockReturnValue(mockSheetsClient);
  });

  const mockGoogleSheetsData = [
    // Header row - 完整的欄位結構 (A-AD)，訂單號碼在索引21(V欄)，付款狀態在索引22(W欄)
    ['Submitted at', '場次時間', '若無合適時間地點', '參加方式', '姓名', 'Email', '手機', '同行者姓名', '同行者 Email', '性別', '年齡', '居住地區', '擁有的手錶類型', '目前擁有手錶的品牌', '對於活動有任何疑問', '個人資料使用與說明同意', 'url', 'utm_campaign', 'utm_source_platform', 'utm_marketing_tactic', 'utm_creative_format', '訂單號碼', '付款狀態', '應付金額', 'PayUni交易號', '付款方式', '付款完成時間', '備註', '報名狀態', '保留1'], // A-AD
    // Data rows - 確保索引21是訂單號碼，索引22是付款狀態，索引23是應付金額
    ['2025-07-11T10:00:00+08:00', '台北 07/20（日）13:20', '', '個人報名', '測試用戶', '<EMAIL>', '0912345678', '', '', '男', '30-39', '北部地區', '機械錶', 'Rolex', '測試問題', '是', 'http://localhost:3000', 'test_campaign', 'facebook', 'social_media', 'image', 'pangea_1234567890', '待付款', '1500', '', '', '', '', '2', ''], // 待付款訂單
    ['2025-07-11T10:00:00+08:00', '台北 07/21（一）14:30', '', '個人報名', '已付款用戶', '<EMAIL>', '0987654321', '', '', '女', '20-29', '中部地區', '石英錶', 'Omega', '', '是', 'http://localhost:3000', '', '', '', '', 'pangea_9876543210', '已完成', '1500', 'TXN123456', '信用卡', '2025-07-11T11:00:00+08:00', '', '1', ''], // 已付款訂單
    ['2025-07-11T10:00:00+08:00', '台北 07/22（二）15:40', '', '個人報名', '重試用戶', '<EMAIL>', '0911111111', '', '', '男', '40-49', '南部地區', '機械錶', 'Seiko', '', '是', 'http://localhost:3000', '', '', '', '', 'pangea_retry_1234567890', '待付款', '1500', '', '', '', '', '2', ''], // 已經是重試的訂單
  ];

  const mockPaymentResponse = {
    MerID: 'S01421169',
    Version: '1.0',
    EncryptInfo: 'encrypted_data_here',
    HashInfo: 'hash_info_here',
    paymentUrl: 'https://sandbox-api.payuni.com.tw/api/payment',
  };

  describe('成功案例', () => {
    test('應該成功處理重新付款請求', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toContain('重新付款請求已建立');
      expect(data.paymentData.MerID).toBe('S01421169');
      expect(data.paymentData.EncryptInfo).toBe('encrypted_data_here');
      expect(data.paymentData.HashInfo).toBe('hash_info_here');
      
      // 檢查是否更新了 Google Sheets
      const { updateSheetDataEdge } = require('@/lib/google-sheets-edge');
      expect(updateSheetDataEdge).toHaveBeenCalled();
    });

    test('應該生成唯一的重新付款訂單號碼', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      // 第一次重新付款
      const request1 = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response1 = await POST(request1);
      const data1 = await response1.json();

      // 等待一毫秒確保時間戳不同
      await new Promise(resolve => setTimeout(resolve, 1));

      // 第二次重新付款
      const request2 = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response2 = await POST(request2);
      const data2 = await response2.json();

      expect(data1.message).not.toBe(data2.message);
      expect(data1.message).toContain('重新付款請求已建立');
      expect(data2.message).toContain('重新付款請求已建立');
    });

    test('應該正確更新 Google Sheets 中的重新付款訂單號碼', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      // 檢查是否更新了 Google Sheets
      const { updateSheetDataEdge } = require('@/lib/google-sheets-edge');
      expect(updateSheetDataEdge).toHaveBeenCalled();
    });

    test('應該使用原始訂單的資料建立新付款', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const createPaymentCall = createPaymentRequestEdge.mock.calls[0][0];
      expect(createPaymentCall.TradeAmt).toBe(1500);
      expect(createPaymentCall.ProdDesc).toBe('錶匠體驗機芯拆解');
      expect(createPaymentCall.UsrMail).toBe('<EMAIL>');
      expect(createPaymentCall.MerTradeNo).toMatch(/^pangea_\d+$/);
    });
  });

  describe('驗證錯誤', () => {
    test('應該拒絕空的訂單號碼', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: '' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少訂單號碼');
    });

    test('應該拒絕缺少訂單號碼的請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('缺少訂單號碼');
    });

    test('應該拒絕已付款的訂單', async () => {
      const { getSheetData } = require('@/lib/google-sheets');

      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_9876543210' }), // 已付款的訂單
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('此訂單已完成付款');
    });

    test('應該拒絕找不到的訂單', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'nonexistent_order' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('找不到指定的訂單');
    });
  });

  describe('錯誤處理', () => {
    test('應該處理 Google Sheets 查詢失敗', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');

      getSheetDataEdge.mockRejectedValue(new Error('Google Sheets API 錯誤'));

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('查詢訂單資料失敗');
    });

    test('應該處理 PayUni 創建付款失敗', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockImplementation(() => {
        throw new Error('PayUni API 錯誤');
      });

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('查詢訂單資料失敗');
    });

    test('應該處理 Google Sheets 更新失敗', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      // Mock updateSheetDataEdge to fail
      const { updateSheetDataEdge } = require('@/lib/google-sheets-edge');
      updateSheetDataEdge.mockRejectedValue(new Error('更新失敗'));

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('查詢訂單資料失敗');
    });

    test('應該處理無效的 JSON 請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('處理失敗，請稍後再試');
    });
  });

  describe('訂單號碼格式', () => {
    test('應該正確處理原始訂單號碼', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(data.message).toContain('重新付款請求已建立');
      expect(data.message).toContain('pangea_');
    });

    test('應該正確處理已經是重新付款的訂單號碼', async () => {
      const { getSheetDataEdge } = require('@/lib/google-sheets-edge');
      const { createPaymentRequestEdge, calculateATMExpireDate } = require('@/lib/payuni-edge');

      getSheetDataEdge.mockResolvedValue(mockGoogleSheetsData);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequestEdge.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_retry_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(data.message).toContain('重新付款請求已建立');
      expect(data.success).toBe(true);
    });
  });
});
