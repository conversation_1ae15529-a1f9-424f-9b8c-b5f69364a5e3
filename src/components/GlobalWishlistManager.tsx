'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import FloatingWishlistButton from './FloatingWishlistButton';
import WishlistModal from './WishlistModal';
import WatchAppointmentModal from './WatchAppointmentModal';
import { WishlistItem } from '@/hooks/useWishlist';

// 定義需要顯示浮動按鈕的頁面路徑
const SHOW_WISHLIST_PATHS = [
  '/pre-owned',
  '/pre-owned-watches',
];

// 檢查是否應該顯示浮動按鈕
const shouldShowWishlist = (pathname: string): boolean => {
  return SHOW_WISHLIST_PATHS.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  );
};

export default function GlobalWishlistManager() {
  const pathname = usePathname();
  const [isWishlistModalOpen, setIsWishlistModalOpen] = useState(false);
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false);
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);

  // 處理鑑賞清單預約
  const handleWishlistBooking = (items: WishlistItem[]) => {
    setWishlistItems(items);
    setIsWishlistModalOpen(false);
    setIsAppointmentModalOpen(true);
  };

  // 如果不在指定頁面，不渲染任何內容
  if (!shouldShowWishlist(pathname)) {
    return null;
  }

  return (
    <>
      {/* 浮動鑑賞清單按鈕 */}
      <FloatingWishlistButton 
        onOpenWishlist={() => setIsWishlistModalOpen(true)}
      />

      {/* 鑑賞清單 Modal */}
      <WishlistModal
        isOpen={isWishlistModalOpen}
        onClose={() => setIsWishlistModalOpen(false)}
        onProceedToBooking={handleWishlistBooking}
      />

      {/* 預約鑑賞 Modal - 鑑賞清單模式 */}
      <WatchAppointmentModal
        isOpen={isAppointmentModalOpen}
        onClose={() => setIsAppointmentModalOpen(false)}
        watchName=""
        mode="wishlist"
        wishlistItems={wishlistItems}
      />
    </>
  );
}
