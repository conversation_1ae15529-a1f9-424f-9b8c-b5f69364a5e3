'use client';

import Link from 'next/link';
import LazyImage from './LazyImage';
import { BlogListItem, formatDate, extractTextFromHtml } from '@/types/blog';

interface BlogCardProps {
  post: BlogListItem;
}

const BlogCard = ({ post }: BlogCardProps) => {
  // 從 SEO 描述中提取摘要
  const excerpt = extractTextFromHtml(post.seoDescription, 120);

  // 確保 publishDate 是 Date 物件
  const publishDate = post.publishDate instanceof Date ? post.publishDate : new Date(post.publishDate);

  return (
    <Link href={`/blog/${post.slug}`} className="block h-full w-full">
      <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-slate-200 blog-card h-full w-full flex flex-col">

        {/* 文章圖片 */}
        <div className="relative w-full h-64 overflow-hidden flex-shrink-0">
          <LazyImage
            src={post.thumbnail || '/images/placeholder-blog.svg'}
            alt={post.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            placeholder="/images/placeholder-blog.svg"
          />
        </div>

        {/* 文章資訊 */}
        <div className="p-6 flex flex-col flex-grow min-w-0">
          {/* 發布日期 */}
          <div className="flex items-center text-sm mb-3 truncate" style={{ color: '#2b354d', opacity: 0.7 }}>
            <time dateTime={publishDate.toISOString()}>
              {formatDate(publishDate)}
            </time>
          </div>

          {/* 文章標題 */}
          <h3 className="text-xl font-semibold mb-3 line-clamp-2 leading-tight transition-colors min-w-0 break-words" style={{ color: '#2b354d' }}>
            {post.title}
          </h3>

          {/* 文章摘要 */}
          {excerpt && (
            <p className="line-clamp-3 leading-relaxed mt-auto min-w-0 break-words" style={{ color: '#2b354d', opacity: 0.8 }}>
              {excerpt}
            </p>
          )}
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
