'use client';


import { X, Trash2, Heart, ArrowRight } from 'lucide-react';
import { useWishlist, WishlistItem } from '@/hooks/useWishlist';
import OptimizedImage from './OptimizedImage';

interface WishlistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceedToBooking: (items: WishlistItem[]) => void;
}

export default function WishlistModal({ isOpen, onClose, onProceedToBooking }: WishlistModalProps) {
  const { wishlist, removeFromWishlist, clearWishlist } = useWishlist();

  if (!isOpen) return null;

  const handleProceedToBooking = () => {
    if (wishlist.length > 0) {
      onProceedToBooking(wishlist);
    }
  };

  const formatPrice = (price: number) => {
    return `NT$${new Intl.NumberFormat('zh-TW').format(price)}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div 
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold" style={{ color: '#2b354d' }}>
            我的鑑賞清單 ({wishlist.length} 件)
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors cursor-pointer"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {wishlist.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Heart className="w-16 h-16 mx-auto" />
              </div>
              <p className="text-gray-500 text-lg">您的鑑賞清單是空的</p>
              <p className="text-gray-400 text-sm mt-2">瀏覽錶款並點擊「加入鑑賞清單」來添加您感興趣的錶款</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 清單操作 */}
              <div className="flex justify-between items-center mb-6">
                <p className="text-gray-600">已選擇 {wishlist.length} 件錶款</p>
                <button
                  onClick={clearWishlist}
                  className="text-red-500 hover:text-red-700 text-sm flex items-center gap-1 transition-colors cursor-pointer"
                >
                  <Trash2 className="w-4 h-4" />
                  清空清單
                </button>
              </div>

              {/* 錶款列表 */}
              {wishlist.map((item) => (
                <div key={item.id} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                  {/* 錶款圖片 */}
                  <div className="w-20 h-20 flex-shrink-0">
                    {item.image ? (
                      <OptimizedImage
                        src={item.image}
                        alt={`${item.brand} ${item.productName}`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover rounded-md"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                        <span className="text-gray-400 text-xs">無圖片</span>
                      </div>
                    )}
                  </div>

                  {/* 錶款資訊 */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-lg truncate" style={{ color: '#2b354d' }}>
                      {item.brand} {item.productName}
                    </h4>
                    <p className="text-lg font-bold mt-1" style={{ color: '#2b354d' }}>
                      {formatPrice(item.price)}
                    </p>
                  </div>

                  {/* 移除按鈕 */}
                  <button
                    onClick={() => removeFromWishlist(item.id)}
                    className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors cursor-pointer"
                    aria-label="移除此錶款"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Modal Footer */}
        {wishlist.length > 0 && (
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="flex-1 py-3 px-6 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-100 transition-colors cursor-pointer"
              >
                繼續瀏覽
              </button>
              <button
                onClick={handleProceedToBooking}
                className="flex-1 py-3 px-6 rounded-lg font-medium transition-colors cursor-pointer"
                style={{
                  backgroundColor: '#2b354d',
                  color: '#ffffff'
                }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                <div className="flex items-center justify-center">
                  預約鑑賞 ({wishlist.length} 件)
                  <ArrowRight className="ml-2 h-5 w-5" />
                </div>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
