'use client';

import { useState } from 'react';
import { Watch } from '@/types/watch';
// import { useWishlist } from '@/hooks/useWishlist';
import OptimizedImage from './OptimizedImage';
import WatchAppointmentModal from './WatchAppointmentModal';
import { X, ChevronLeft, ChevronRight, Shield, Clock, MapPin, Calendar, Phone, MessageSquare, ExternalLink, CheckCircle } from 'lucide-react';
import { useAnimationCompatibility } from '@/hooks/useBrowserDetection';

interface WatchDetailClientProps {
  watch: Watch;
}

export default function WatchDetailClient({ watch }: WatchDetailClientProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [isAppointmentModalOpen, setIsAppointmentModalOpen] = useState(false);
  // const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);

  // const { addToWishlist, isInWishlist } = useWishlist();

  // iOS 檢測和動畫兼容性
  useAnimationCompatibility();

  // 處理圖片陣列 - 從 image 和 thumbnail 欄位解析
  const parseImageUrls = (imageString: string): string[] => {
    if (!imageString) return [];
    return imageString
      .split(/[,\n\r]+/)
      .map(url => url.trim())
      .filter(url => url && url.startsWith('http'));
  };

  const images = [
    ...parseImageUrls(watch.image || ''),
    ...parseImageUrls(watch.thumbnail || ''),
  ].filter((img, index, arr) => img && arr.indexOf(img) === index); // 去重

  // 格式化價格顯示
  const formatPrice = (price: number) => {
    return `NT$${new Intl.NumberFormat('zh-TW').format(price)}`;
  };

  // 處理圖片點擊
  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
    setIsLightboxOpen(true);
  };

  // 關閉燈箱
  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  // 上一張圖片
  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  // 下一張圖片
  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  // 處理立即預約 (暫時隱藏)
  /*
  const handleBookingClick = () => {
    setIsAppointmentModalOpen(true);
  };

  // 處理加入鑑賞清單 (暫時隱藏)
  const handleAddToWishlist = () => {
    if (isInWishlist(watch.id)) return;

    setIsAddingToWishlist(true);

    const wishlistItem = {
      id: watch.id,
      productName: watch.productName,
      brand: watch.brand,
      price: watch.price,
      image: watch.thumbnail || watch.image,
      slug: watch.seoSlug || watch.id
    };

    addToWishlist(wishlistItem);

    // 提供視覺回饋 - 縮短時間
    setTimeout(() => {
      setIsAddingToWishlist(false);
    }, 500);
  };
  */

  // 信任標章資料
  const trustBadges = [
    { icon: Shield, text: '100% 正品保證' },
    { icon: Clock, text: '一年機芯保固' },
    { icon: CheckCircle, text: '安全預約鑑賞' }
  ];

  // 預約流程步驟
  const bookingSteps = [
    {
      title: '填寫表單',
      description: '在下方表單選擇您偏好的鑑賞地點與時間，並留下您的聯絡資訊。',
      icon: MessageSquare
    },
    {
      title: '專人聯繫',
      description: '我們會在 48 小時內與您聯繫，確認最終的預約時間。',
      icon: Phone
    },
    {
      title: '親臨鑑賞',
      description: '在約定時間前往指定地點，享受一對一的專屬鑑賞體驗。',
      icon: Calendar
    }
  ];

  // 鑑賞地點資料
  const viewingLocations = [
    {
      name: '台北總店',
      address: '信義區松仁路',
      mapUrl: 'https://www.google.com/maps/search/台北市信義區松仁路'
    },
    {
      name: '台北東區',
      address: '忠孝東路四段',
      mapUrl: 'https://www.google.com/maps/search/台北市忠孝東路四段'
    },
    {
      name: '台中店',
      address: '西屯區台灣大道',
      mapUrl: 'https://www.google.com/maps/search/台中市西屯區台灣大道'
    }
  ];

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
        {/* 圖片展示區域 */}
        <div className="w-full">
          {/* 主圖片 */}
          {images.length > 0 && (
            <div className="relative w-full watch-detail-main-image rounded-md overflow-hidden">
              <OptimizedImage
                src={images[currentImageIndex]}
                alt={`${watch.brand} ${watch.productName}`}
                width={800}
                height={600}
                className="w-full h-auto object-cover cursor-pointer hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={true}
                loading="eager"
                onClick={() => handleImageClick(currentImageIndex)}
              />
            </div>
          )}

          {/* 縮圖列表 */}
          {images.length > 1 && (
            <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-4 xl:grid-cols-6 w-full watch-detail-thumbnails">
              {images.slice(0, 12).map((image, index) => (
                <div
                  key={index}
                  className={`relative watch-detail-thumbnail rounded-md overflow-hidden cursor-pointer border transition-all ${
                    index === currentImageIndex ? 'border-[#2b354d]' : 'border-transparent hover:border-gray-300'
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                >
                  <OptimizedImage
                    src={image}
                    alt={`${watch.brand} ${watch.productName} - 圖片 ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 25vw, (max-width: 1200px) 12.5vw, 8.33vw"
                    priority={index < 4}
                    loading={index < 4 ? 'eager' : 'lazy'}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 手錶資訊 */}
        <div>
          <div className="lg:pl-8">
            {/* 品牌 */}
            <div className="mb-3">
              <h2 className="text-xl font-bold" style={{ color: '#2b354d' }}>
                {watch.brand}
              </h2>
            </div>

            {/* 產品名稱 */}
            <div className="mb-4">
              <h1 className="text-2xl font-bold" style={{ color: '#2b354d' }}>
                {watch.productName}
              </h1>
            </div>

            {/* 價格 */}
            {watch.price && (
              <div className="mb-6">
                <p className="text-2xl font-bold" style={{ color: '#2b354d' }}>
                  {formatPrice(watch.price)}
                </p>
              </div>
            )}

            {/* 詳細描述 */}
            {watch.listingDescription && (
              <div className="mb-6">
                {/* 檢查是否包含 HTML 標籤 */}
                {watch.listingDescription.includes('<') ? (
                  // 如果包含 HTML 標籤，使用原本的方式
                  <div
                    className="text-base leading-relaxed watch-description"
                    style={{ color: '#2b354d' }}
                    dangerouslySetInnerHTML={{ __html: watch.listingDescription }}
                  />
                ) : (
                  // 如果是純文字，處理換行
                  <div className="text-base leading-relaxed" style={{ color: '#2b354d' }}>
                    {watch.listingDescription.split(/\r?\n/).map((line, index) => {
                      const trimmedLine = line.trim();
                      if (!trimmedLine) return null;

                      // 檢查是否為網址（更完整的檢測）
                      const urlRegex = /^https?:\/\/.+/i;
                      const isUrl = urlRegex.test(trimmedLine);

                      return (
                        <div key={index} className="mb-1">
                          {isUrl ? (
                            <a
                              href={trimmedLine}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm underline hover:opacity-80 break-all"
                              style={{ color: '#2b354d' }}
                            >
                              {trimmedLine}
                            </a>
                          ) : (
                            <span className="font-medium">{trimmedLine}</span>
                          )}
                        </div>
                      );
                    }).filter(Boolean)}
                  </div>
                )}
              </div>
            )}

            {/* 基本資訊 - Tag 形式 */}
            <div className="mb-6 flex flex-wrap gap-2">
              {watch.boxAndPaper && (
                <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                  {watch.boxAndPaper}
                </span>
              )}
              {watch.movement && (
                <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                  {watch.movement}
                </span>
              )}
              {watch.caseSize && (
                <span className="inline-block px-3 py-1 bg-gray-100 rounded-full text-sm font-medium" style={{ color: '#2b354d' }}>
                  {watch.caseSize}
                </span>
              )}
            </div>

            {/* 信任標章 */}
            <div className="mb-6">
              <div className="flex flex-wrap gap-4">
                {trustBadges.map((badge, index) => {
                  const IconComponent = badge.icon;
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <IconComponent className="w-4 h-4" style={{ color: '#f59e0b' }} />
                      <span className="text-sm font-medium" style={{ color: '#2b354d' }}>
                        {badge.text}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* CTA 按鈕 */}
            <div className="mb-8">
              <div className="flex flex-col sm:flex-row gap-3">
                {/* 主要 CTA - 立即私訊洽詢 */}
                <a
                  href="https://m.me/weaven.co"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 h-12 text-base font-semibold rounded-md transition-all duration-200 hover:opacity-90 cursor-pointer flex items-center justify-center"
                  style={{
                    backgroundColor: '#2b354d',
                    color: '#ffffff',
                    textDecoration: 'none'
                  }}
                >
                  <div className="flex items-center justify-center">
                    立即私訊洽詢
                    <MessageSquare className="ml-2 h-5 w-5" />
                  </div>
                </a>

                {/* 次要 CTA - 加入清單 (暫時隱藏) */}
                {/*
                <Button
                  onClick={handleAddToWishlist}
                  disabled={isInWishlist(watch.id) || isAddingToWishlist}
                  variant="outline"
                  className="flex-1 h-12 text-base font-medium rounded-md transition-all duration-200 cursor-pointer"
                  style={{
                    borderColor: '#2b354d',
                    color: '#2b354d'
                  }}
                >
                  {isAddingToWishlist ? (
                    <>
                      <Plus className="w-4 h-4 mr-2 animate-spin" />
                      加入中...
                    </>
                  ) : isInWishlist(watch.id) ? (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" style={{ color: '#f59e0b' }} />
                      <span style={{ color: '#f59e0b' }}>已加入</span>
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      加入鑑賞清單
                    </>
                  )}
                </Button>
                */}
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* 預約流程說明 - 頁面下方區塊 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 mt-12">
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-8 text-center" style={{ color: '#2b354d' }}>
            預約流程
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {bookingSteps.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-lg p-6 text-center">
                  <div className="flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 mr-2" style={{ color: '#f59e0b' }} />
                    <h4 className="font-semibold text-lg" style={{ color: '#2b354d' }}>
                      {step.title}
                    </h4>
                  </div>
                  <p className="text-base text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* 鑑賞地點 - 頁面下方區塊 */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-8 text-center" style={{ color: '#2b354d' }}>
            鑑賞地點
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {viewingLocations.map((location, index) => (
              <a
                key={index}
                href={location.mapUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200 group text-center"
              >
                <div className="flex items-center justify-center mb-3">
                  <MapPin className="w-6 h-6 mr-2" style={{ color: '#f59e0b' }} />
                  <h4 className="font-semibold group-hover:text-blue-600 transition-colors text-lg" style={{ color: '#2b354d' }}>
                    {location.name}
                  </h4>
                  <ExternalLink className="w-4 h-4 ml-2 text-gray-400 group-hover:text-[#2b354d] transition-colors" />
                </div>
                <p className="text-base text-gray-600">
                  {location.address}
                </p>
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* 燈箱 */}
      {isLightboxOpen && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center" style={{ zIndex: 99999 }}>
          <div className="relative max-w-4xl max-h-full p-4">
            {/* 關閉按鈕 */}
            <button
              onClick={closeLightbox}
              className="text-white hover:text-gray-300 transition-colors"
              style={{
                position: 'fixed',
                top: '16px',
                right: '16px',
                zIndex: 999999,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                borderRadius: '50%',
                width: '48px',
                height: '48px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              <X size={24} />
            </button>

            {/* 圖片 */}
            <div className="relative">
              <OptimizedImage
                src={images[currentImageIndex]}
                alt={`${watch.brand} ${watch.productName}`}
                width={800}
                height={600}
                className="max-w-full max-h-[80vh] object-contain"
                priority={true}
                loading="eager"
              />

              {/* 導航按鈕 */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={previousImage}
                    className="text-white hover:text-gray-300 transition-all duration-200"
                    style={{
                      position: 'fixed',
                      left: '16px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 999999,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '50%',
                      width: '48px',
                      height: '48px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
                    }}
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <button
                    onClick={nextImage}
                    className="text-white hover:text-gray-300 transition-all duration-200"
                    style={{
                      position: 'fixed',
                      right: '16px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      zIndex: 999999,
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '50%',
                      width: '48px',
                      height: '48px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: 'none',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
                    }}
                  >
                    <ChevronRight size={16} />
                  </button>
                </>
              )}
            </div>

            {/* 圖片計數 */}
            {images.length > 1 && (
              <div className="text-center text-white mt-4 text-lg">
                {currentImageIndex + 1} / {images.length}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 預約表單 Modal */}
      <WatchAppointmentModal
        isOpen={isAppointmentModalOpen}
        onClose={() => setIsAppointmentModalOpen(false)}
        watchName={`${watch.brand} ${watch.productName}`}
        mode="single"
        watchImage={watch.thumbnail || watch.image}
        watchPrice={watch.price}
      />
    </>
  );
}