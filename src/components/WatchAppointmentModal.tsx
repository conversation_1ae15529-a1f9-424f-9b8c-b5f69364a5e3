'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HoneypotField, TimestampField, SecurityTokenField } from '@/components/ui/honeypot-field';
import { useFormSecurity } from '@/hooks/useFormSecurity';
import { WishlistItem } from '@/hooks/useWishlist';
import OptimizedImage from './OptimizedImage';
import { Phone, Mail, MessageSquare, ArrowRight } from 'lucide-react';

interface WatchAppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  watchName: string;
  mode?: 'single' | 'wishlist';
  wishlistItems?: WishlistItem[];
  watchImage?: string;
  watchPrice?: number;
}

interface FormData {
  watchName: string;
  watchItems: string; // 用於多錶款模式
  customerName: string;
  phone: string;
  email: string;
  location: string;
  preferredDate: string;
  notes: string;
}

const VIEWING_LOCATIONS = [
  { value: 'taipei-main', label: '台北總店 - 信義區松仁路' },
  { value: 'taipei-east', label: '台北東區 - 忠孝東路四段' },
  { value: 'taichung', label: '台中店 - 西屯區台灣大道' }
];

export default function WatchAppointmentModal({
  isOpen,
  onClose,
  watchName,
  mode = 'single',
  wishlistItems = [],
  watchImage,
  watchPrice
}: WatchAppointmentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 根據模式設定初始錶款資料
  const getInitialWatchData = () => {
    if (mode === 'wishlist' && wishlistItems.length > 0) {
      return {
        watchName: '',
        watchItems: wishlistItems.map(item => `${item.brand} ${item.productName}`).join(',')
      };
    }
    return {
      watchName: watchName,
      watchItems: ''
    };
  };

  // 生成可預約日期（T+2 開始）
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 2; i <= 14; i++) { // T+2 到 T+14，共13天
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        value: date.toISOString().split('T')[0],
        label: date.toLocaleDateString('zh-TW', {
          month: 'long',
          day: 'numeric',
          weekday: 'short'
        })
      });
    }
    return dates;
  };

  const [formData, setFormData] = useState<FormData>({
    ...getInitialWatchData(),
    customerName: '',
    phone: '',
    email: '',
    location: '',
    preferredDate: '',
    notes: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 表單安全防護
  const {
    honeypotValue,
    formStartTime,
    securityToken,
    setHoneypotValue,
    validateBeforeSubmit,
    resetSecurity,
  } = useFormSecurity();

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = '請輸入您的姓名';
    }

    // 電話或信箱至少要填一個
    if (!formData.phone.trim() && !formData.email.trim()) {
      newErrors.contact = '請至少填寫電話或信箱其中一項';
    }

    // 電話格式驗證（如果有填寫）
    if (formData.phone.trim()) {
      // 移除所有空格和連字符
      const cleanPhone = formData.phone.replace(/[\s-]/g, '');
      // 支援格式：09xxxxxxxx, +8869xxxxxxxx, 8869xxxxxxxx
      const phoneRegex = /^(\+?886-?9|09)\d{8}$/;
      if (!phoneRegex.test(cleanPhone)) {
        newErrors.phone = '請輸入正確的手機號碼格式（例：09xxxxxxxx）';
      }
    }

    // 信箱格式驗證（如果有填寫）
    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = '請輸入正確的信箱格式';
      }
    }

    if (!formData.location) {
      newErrors.location = '請選擇鑑賞地點';
    }

    if (!formData.preferredDate) {
      newErrors.preferredDate = '請選擇預約日期';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // 安全驗證
    const securityValidation = validateBeforeSubmit();
    if (!securityValidation.isValid) {
      alert(securityValidation.reason);
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/watch-appointment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          honeypot: honeypotValue,
          formStartTime,
          securityToken,
          submissionTime: Date.now(),
        }),
      });

      if (response.ok) {
        alert('預約申請已送出！我們將盡快與您聯繫確認預約時間。');
        onClose();
        // 重置表單
        setFormData({
          ...getInitialWatchData(),
          customerName: '',
          phone: '',
          email: '',
          location: '',
          preferredDate: '',
          notes: ''
        });
        resetSecurity();
      } else {
        const errorData = await response.json();
        alert(errorData.message || '送出失敗，請稍後再試');
      }
    } catch (error) {
      console.error('預約送出錯誤:', error);
      alert('網路錯誤，請稍後再試');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose();
    setErrors({});
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff' }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-bold" style={{ color: '#2b354d' }}>
            {mode === 'wishlist'
              ? `我的鑑賞清單 (${wishlistItems.length} 件)`
              : `預約鑑賞：「${watchName}」`
            }
          </DialogTitle>
        </DialogHeader>

        {/* 單一錶款顯示 (僅在 single 模式) */}
        {mode === 'single' && (
          <div className="mb-6">
            <div className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
              {/* 錶款圖片 */}
              <div className="w-16 h-16 flex-shrink-0">
                {watchImage ? (
                  <OptimizedImage
                    src={watchImage}
                    alt={watchName}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover rounded-md"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                    <span className="text-gray-400 text-xs">無圖片</span>
                  </div>
                )}
              </div>

              {/* 錶款資訊 */}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm truncate" style={{ color: '#2b354d' }}>
                  {watchName}
                </h4>
                {watchPrice && (
                  <p className="text-sm font-semibold mt-1" style={{ color: '#2b354d' }}>
                    NT${new Intl.NumberFormat('zh-TW').format(watchPrice)}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 鑑賞清單顯示 (僅在 wishlist 模式) */}
        {mode === 'wishlist' && wishlistItems.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#2b354d' }}>
              您選擇的錶款
            </h3>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {wishlistItems.map((item) => (
                <div key={item.id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                  {/* 錶款圖片 */}
                  <div className="w-16 h-16 flex-shrink-0">
                    {item.image ? (
                      <OptimizedImage
                        src={item.image}
                        alt={`${item.brand} ${item.productName}`}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover rounded-md"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 rounded-md flex items-center justify-center">
                        <span className="text-gray-400 text-xs">無圖片</span>
                      </div>
                    )}
                  </div>

                  {/* 錶款資訊 */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate" style={{ color: '#2b354d' }}>
                      {item.brand} {item.productName}
                    </h4>
                    <p className="text-sm font-semibold mt-1" style={{ color: '#2b354d' }}>
                      NT${new Intl.NumberFormat('zh-TW').format(item.price)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 預約表單 */}
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* 安全防護欄位 */}
            <HoneypotField value={honeypotValue} onChange={setHoneypotValue} />
            <TimestampField startTime={formStartTime} />
            <SecurityTokenField token={securityToken} />



            {/* 客戶姓名 */}
            <div>
              <Label htmlFor="customerName" className="text-sm font-medium" style={{ color: '#2b354d' }}>
                您的姓名 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="customerName"
                value={formData.customerName}
                onChange={(e) => handleInputChange('customerName', e.target.value)}
                className="mt-1"
                placeholder="請輸入您的姓名"
              />
              {errors.customerName && (
                <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>
              )}
            </div>

            {/* 聯絡方式 */}
            <div>
              <div className="mb-2">
                <span className="text-sm font-medium" style={{ color: '#2b354d' }}>
                  聯絡方式 <span className="text-red-500">*</span>
                </span>
                <span className="text-xs text-gray-500 ml-2">
                  (電話或信箱請至少填寫一項)
                </span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone" className="text-sm font-medium flex items-center" style={{ color: '#2b354d' }}>
                  <Phone className="w-4 h-4 mr-1" />
                  聯絡電話
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="mt-1"
                  placeholder="09xxxxxxxx"
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                )}
              </div>
              <div>
                <Label htmlFor="email" className="text-sm font-medium flex items-center" style={{ color: '#2b354d' }}>
                  <Mail className="w-4 h-4 mr-1" />
                  信箱
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="mt-1"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>
              </div>
              {errors.contact && (
                <p className="text-red-500 text-sm mt-2">{errors.contact}</p>
              )}
            </div>

            {/* 鑑賞地點與預約日期 - 左右並陳 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 鑑賞地點 */}
              <div>
                <Label htmlFor="location" className="text-sm font-medium" style={{ color: '#2b354d' }}>
                  鑑賞地點 <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.location} onValueChange={(value) => handleInputChange('location', value)}>
                  <SelectTrigger className="mt-1 h-9 w-full">
                    <SelectValue placeholder="請選擇鑑賞地點" />
                  </SelectTrigger>
                  <SelectContent style={{ backgroundColor: '#ffffff' }}>
                    {VIEWING_LOCATIONS.map((location) => (
                      <SelectItem key={location.value} value={location.value}>
                        {location.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.location && (
                  <p className="text-red-500 text-sm mt-1">{errors.location}</p>
                )}
              </div>

              {/* 預約日期 */}
              <div>
                <Label htmlFor="preferredDate" className="text-sm font-medium" style={{ color: '#2b354d' }}>
                  預約日期 <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.preferredDate} onValueChange={(value) => handleInputChange('preferredDate', value)}>
                  <SelectTrigger className="mt-1 h-9 w-full">
                    <SelectValue placeholder="請選擇預約日期" />
                  </SelectTrigger>
                  <SelectContent style={{ backgroundColor: '#ffffff' }}>
                    {getAvailableDates().map((date) => (
                      <SelectItem key={date.value} value={date.value}>
                        {date.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  至少需提前兩天預約
                </p>
                {errors.preferredDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.preferredDate}</p>
                )}
              </div>
            </div>

            {/* 備註 */}
            <div>
              <Label htmlFor="notes" className="text-sm font-medium flex items-center" style={{ color: '#2b354d' }}>
                <MessageSquare className="w-4 h-4 mr-1" />
                備註（選填）
              </Label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                className="mt-1 w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="如有特殊需求或偏好時間，請在此說明...如需當天鑑賞也可於此留言或直接私訊詢問"
              />
            </div>

            {/* 提交按鈕 */}
            <div className="flex justify-center pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-2 font-semibold cursor-pointer"
                style={{ backgroundColor: '#2b354d', color: '#ffffff' }}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    送出中...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    確認送出預約
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </div>
                )}
              </Button>
            </div>
          </form>
      </DialogContent>
    </Dialog>
  );
}
