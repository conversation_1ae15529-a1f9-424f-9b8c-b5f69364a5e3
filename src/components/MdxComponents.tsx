// MDX 自定義組件配置
import Image from 'next/image';
import Link from 'next/link';
import CallToAction from './mdx/CallToAction';
import ImageWithCaption from './mdx/ImageWithCaption';
import InfoBox from './mdx/InfoBox';
import Table from './mdx/Table';

// 定義組件 props 類型
interface ComponentProps {
  children?: React.ReactNode;
  className?: string;
  [key: string]: unknown;
}

interface LinkProps extends ComponentProps {
  href?: string;
}

interface ImageProps extends ComponentProps {
  src?: string;
  alt?: string;
}

// 自定義圖片組件
const CustomImage = ({ src, alt }: ImageProps) => {
  if (!src) return null;

  return (
    <span className="block relative w-full my-8">
      <Image
        src={src}
        alt={alt || ''}
        width={800}
        height={400}
        className="rounded-lg object-cover w-full h-auto"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
        quality={90}
      />
    </span>
  );
};

// 自定義連結組件
const CustomLink = ({ href, children, ...props }: LinkProps) => {
  // 內部連結使用 Next.js Link
  if (href?.startsWith('/') || href?.startsWith('#')) {
    return (
      <Link href={href} className="text-blue-600 hover:text-blue-800 underline" {...props}>
        {children}
      </Link>
    );
  }
  
  // 外部連結
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 hover:text-blue-800 underline"
      {...props}
    >
      {children}
    </a>
  );
};

// 自定義標題組件
const CustomH1 = ({ children, ...props }: ComponentProps) => (
  <h1 className="text-4xl font-bold mb-6 text-slate-900 leading-tight" {...props}>
    {children}
  </h1>
);

const CustomH2 = ({ children, ...props }: ComponentProps) => (
  <h2 className="text-3xl font-semibold mb-4 mt-8 text-slate-900 leading-tight" {...props}>
    {children}
  </h2>
);

const CustomH3 = ({ children, ...props }: ComponentProps) => (
  <h3 className="text-2xl font-semibold mb-3 mt-6 text-slate-900 leading-tight" {...props}>
    {children}
  </h3>
);

// 自定義段落組件
const CustomParagraph = ({ children, ...props }: ComponentProps) => (
  <p className="mb-4 leading-relaxed text-slate-700" {...props}>
    {children}
  </p>
);

// 自定義引用組件
const CustomBlockquote = ({ children, ...props }: ComponentProps) => (
  <blockquote className="border-l-4 border-blue-500 pl-6 my-6 italic text-slate-600 bg-slate-50 py-4 rounded-r-lg" {...props}>
    {children}
  </blockquote>
);

// 自定義程式碼區塊組件
const CustomPre = ({ children, ...props }: ComponentProps) => (
  <pre className="bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto my-6 text-sm" {...props}>
    {children}
  </pre>
);

// 自定義行內程式碼組件
const CustomCode = ({ children, ...props }: ComponentProps) => (
  <code className="bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono" {...props}>
    {children}
  </code>
);

// 自定義列表組件
const CustomUL = ({ children, ...props }: ComponentProps) => (
  <ul className="list-disc list-inside mb-4 space-y-2 text-slate-700" {...props}>
    {children}
  </ul>
);

const CustomOL = ({ children, ...props }: ComponentProps) => (
  <ol className="list-decimal list-inside mb-4 space-y-2 text-slate-700" {...props}>
    {children}
  </ol>
);

const CustomLI = ({ children, ...props }: ComponentProps) => (
  <li className="leading-relaxed" {...props}>
    {children}
  </li>
);

// 自定義表格組件
const CustomTable = ({ children, ...props }: ComponentProps) => (
  <div className="my-4 overflow-x-auto">
    <table className="w-full border-collapse border border-slate-300 rounded-lg overflow-hidden" {...props}>
      {children}
    </table>
  </div>
);

const CustomTHead = ({ children, ...props }: ComponentProps) => (
  <thead {...props}>
    {children}
  </thead>
);

const CustomTBody = ({ children, ...props }: ComponentProps) => (
  <tbody {...props}>
    {children}
  </tbody>
);

const CustomTR = ({ children, ...props }: ComponentProps) => (
  <tr className="even:bg-slate-50 odd:bg-white" {...props}>
    {children}
  </tr>
);

const CustomTH = ({ children, ...props }: ComponentProps) => (
  <th
    className="px-4 py-3 text-left font-medium border border-slate-300 bg-slate-100 text-slate-700"
    {...props}
  >
    {children}
  </th>
);

const CustomTD = ({ children, ...props }: ComponentProps) => (
  <td className="px-4 py-3 border border-slate-300 text-slate-700" {...props}>
    {children}
  </td>
);

// MDX 組件映射
const mdxComponents = {
  h1: CustomH1,
  h2: CustomH2,
  h3: CustomH3,
  p: CustomParagraph,
  img: CustomImage,
  a: CustomLink,
  blockquote: CustomBlockquote,
  pre: CustomPre,
  code: CustomCode,
  ul: CustomUL,
  ol: CustomOL,
  li: CustomLI,
  table: CustomTable,
  thead: CustomTHead,
  tbody: CustomTBody,
  tr: CustomTR,
  th: CustomTH,
  td: CustomTD,
  // 自定義元件
  CallToAction,
  ImageWithCaption,
  InfoBox,
  Table,
} as const;

// 預設匯出供 MDX 使用
export default mdxComponents;
