"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";

interface BrandHeroSectionProps {
  title: string;
  subtitle: string;
  ctaText: string;
  ctaTarget?: string; // 滾動目標元素的 ID
  backgroundImage?: string;
  backgroundVideo?: string;
}

const BrandHeroSection = ({
  title,
  subtitle,
  ctaText,
  ctaTarget = 'core-services',
  backgroundImage,
  backgroundVideo
}: BrandHeroSectionProps) => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const isIOSSafari = useIsIOSSafari();

  useEffect(() => {
    if (backgroundVideo) {
      const video = document.createElement('video');
      video.onloadeddata = () => setIsVideoLoaded(true);
      video.src = backgroundVideo;
    }
  }, [backgroundVideo]);

  const handleCtaClick = () => {
    const element = document.getElementById(ctaTarget);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 背景媒體 */}
      {backgroundVideo && (
        <video
          autoPlay
          muted
          loop
          playsInline
          className={cn(
            "absolute inset-0 w-full h-full object-cover transition-opacity duration-1000",
            isVideoLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoadedData={() => setIsVideoLoaded(true)}
        >
          <source src={backgroundVideo} type="video/mp4" />
        </video>
      )}
      
      {backgroundImage && (
        <div
          className={cn(
            "absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat transition-opacity duration-1000",
            backgroundVideo && isVideoLoaded ? "opacity-0" : "opacity-100"
          )}
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}

      {/* 遮罩層 */}
      <div className="absolute inset-0 bg-black/40" />

      {/* 內容容器 */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {isIOSSafari ? (
          <div className="space-y-8">
            <h1 className="text-3xl md:text-5xl font-bold text-white leading-tight">
              {title}
            </h1>
            <p className="text-base md:text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
              {subtitle}
            </p>
            <div className="pt-4">
              <button
                onClick={handleCtaClick}
                className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              >
                {ctaText}
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <ScrollTriggerMotion
              variants={titleScrollVariants}
              className="mb-8"
            >
              <h1 className="text-3xl md:text-5xl font-bold text-white leading-tight">
                {title}
              </h1>
            </ScrollTriggerMotion>

            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.3}
              className="mb-8"
            >
              <p className="text-base md:text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            </ScrollTriggerMotion>

            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.6}
              className="pt-4"
            >
              <button
                onClick={handleCtaClick}
                className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 cursor-pointer"
              >
                {ctaText}
              </button>
            </ScrollTriggerMotion>
          </div>
        )}
      </div>

      {/* 滾動提示 */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

export default BrandHeroSection;
