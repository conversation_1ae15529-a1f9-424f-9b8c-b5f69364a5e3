"use client";

import Image from "next/image";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";
import { ExternalLink, Droplets, Shield, Sparkles } from "lucide-react";

interface AquaPureSectionProps {
  title: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  image: string;
  features?: string[];
}

const AquaPureSection = ({
  title,
  description,
  ctaText,
  ctaLink,
  image,
  features = []
}: AquaPureSectionProps) => {
  const isIOSSafari = useIsIOSSafari();

  const handleCtaClick = () => {
    window.open(ctaLink, '_blank', 'noopener,noreferrer');
  };

  const featureIcons = [
    <Droplets key="droplets" className="w-5 h-5" />,
    <Shield key="shield" className="w-5 h-5" />,
    <Sparkles key="sparkles" className="w-5 h-5" />
  ];

  return (
    <section className="py-10 md:py-16 lg:py-18 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          
          {/* 左側：圖片 */}
          {isIOSSafari ? (
            <div className="relative">
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src={image}
                  alt="AQUA PURE 產品展示"
                  fill
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 50vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-black/10 to-transparent" />
              </div>
              
              {/* 裝飾元素 */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-[#f59e0b] to-[#ea580c] rounded-full opacity-20 blur-xl" />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-tr from-[#2b354d] to-[#4a5568] rounded-full opacity-10 blur-2xl" />
            </div>
          ) : (
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              className="relative"
            >
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src={image}
                  alt="AQUA PURE 產品展示"
                  fill
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 50vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-black/10 to-transparent" />
              </div>
              
              {/* 裝飾元素 */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-[#f59e0b] to-[#ea580c] rounded-full opacity-20 blur-xl" />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-tr from-[#2b354d] to-[#4a5568] rounded-full opacity-10 blur-2xl" />
            </ScrollTriggerMotion>
          )}

          {/* 右側：內容 */}
          <div className="space-y-8">
            {isIOSSafari ? (
              <div className="space-y-6">
                <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] leading-tight">
                  {title}
                </h2>
                <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                  {description}
                </p>
              </div>
            ) : (
              <>
                <ScrollTriggerMotion
                  variants={titleScrollVariants}
                  className="mb-6"
                >
                  <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] leading-tight">
                    {title}
                  </h2>
                </ScrollTriggerMotion>

                <ScrollTriggerMotion
                  variants={contentScrollVariants}
                  delay={0.2}
                  className="mb-8"
                >
                  <p className="text-base md:text-lg text-gray-600 leading-relaxed">
                    {description}
                  </p>
                </ScrollTriggerMotion>
              </>
            )}

            {/* 特色功能列表 */}
            {features.length > 0 && (
              <div className="space-y-4">
                {features.map((feature, index) => (
                  isIOSSafari ? (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-[#f59e0b]/10 rounded-full flex items-center justify-center">
                        <div className="text-[#f59e0b]">
                          {featureIcons[index % featureIcons.length]}
                        </div>
                      </div>
                      <span className="text-gray-700 font-medium">{feature}</span>
                    </div>
                  ) : (
                    <ScrollTriggerMotion
                      key={index}
                      variants={contentScrollVariants}
                      delay={0.3 + index * 0.1}
                      className="flex items-center space-x-3"
                    >
                      <div className="flex-shrink-0 w-8 h-8 bg-[#f59e0b]/10 rounded-full flex items-center justify-center">
                        <div className="text-[#f59e0b]">
                          {featureIcons[index % featureIcons.length]}
                        </div>
                      </div>
                      <span className="text-gray-700 font-medium">{feature}</span>
                    </ScrollTriggerMotion>
                  )
                ))}
              </div>
            )}

            {/* CTA 按鈕 */}
            {isIOSSafari ? (
              <div className="pt-4">
                <button
                  onClick={handleCtaClick}
                  className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 group cursor-pointer"
                >
                  {ctaText}
                  <ExternalLink className="ml-2 w-5 h-5 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </button>
              </div>
            ) : (
              <ScrollTriggerMotion
                variants={contentScrollVariants}
                delay={0.6}
                className="pt-4"
              >
                <button
                  onClick={handleCtaClick}
                  className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-xl transition-all duration-300 transform hover:scale-105 group cursor-pointer"
                >
                  {ctaText}
                  <ExternalLink className="ml-2 w-5 h-5 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                </button>
              </ScrollTriggerMotion>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AquaPureSection;
