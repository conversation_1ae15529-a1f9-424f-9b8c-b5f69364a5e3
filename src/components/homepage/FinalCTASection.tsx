"use client";

import Link from "next/link";
import Image from "next/image";
import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";
import { ArrowRight } from "lucide-react";

interface FinalCTASectionProps {
  title: string;
  subtitle: string;
  primaryCtaText: string;
  primaryCtaLink: string;
  backgroundImage?: string;
}

const FinalCTASection = ({
  title,
  subtitle,
  primaryCtaText,
  primaryCtaLink,
  backgroundImage
}: FinalCTASectionProps) => {
  const isIOSSafari = useIsIOSSafari();

  return (
    <section className="py-10 md:py-16 lg:py-18 bg-white relative overflow-hidden">
      {/* 背景圖片 */}
      {backgroundImage && (
        <div className="absolute inset-0">
          <Image
            src={backgroundImage}
            alt="聯繫我們背景"
            fill
            className="object-cover"
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-white/90" />
        </div>
      )}

      {/* 裝飾元素 */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-[#f59e0b]/10 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-[#2b354d]/10 to-transparent rounded-full blur-3xl" />

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center justify-center">
        {/* 主要內容 */}
        <div className="text-center">
          {isIOSSafari ? (
            <div className="space-y-6">
              <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] leading-tight">
                {title}
              </h2>
              <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {subtitle}
              </p>
            </div>
          ) : (
            <>
              <ScrollTriggerMotion
                variants={titleScrollVariants}
                className="mb-6"
              >
                <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] leading-tight">
                  {title}
                </h2>
              </ScrollTriggerMotion>

              <ScrollTriggerMotion
                variants={contentScrollVariants}
                delay={0.2}
                className="mb-8"
              >
                <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  {subtitle}
                </p>
              </ScrollTriggerMotion>
            </>
          )}

          {/* 主要 CTA 按鈕 - 增加與說明文字的間距 */}
          {isIOSSafari ? (
            <div className="mt-12">
              <Link
                href={primaryCtaLink}
                className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group cursor-pointer"
              >
                {primaryCtaText}
                <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </div>
          ) : (
            <ScrollTriggerMotion
              variants={contentScrollVariants}
              delay={0.4}
              className="mt-12"
            >
              <Link
                href={primaryCtaLink}
                className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white border border-[#2b354d] rounded-full hover:shadow-2xl transition-all duration-300 transform hover:scale-105 group cursor-pointer"
              >
                {primaryCtaText}
                <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </ScrollTriggerMotion>
          )}
        </div>
      </div>
    </section>
  );
};



export default FinalCTASection;
