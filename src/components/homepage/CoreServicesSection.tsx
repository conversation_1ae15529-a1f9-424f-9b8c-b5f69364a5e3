"use client";

import Link from "next/link";
import Image from "next/image";
import { ScrollTriggerMotion, CardMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, cardEntranceVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";
import { ArrowRight } from "lucide-react";

interface ServiceCard {
  id: string;
  title: string;
  description: string;
  image: string;
  link: string;
  icon: React.ReactNode;
}

interface CoreServicesSectionProps {
  title: string;
  services: ServiceCard[];
}

const CoreServicesSection = ({ title, services }: CoreServicesSectionProps) => {
  const isIOSSafari = useIsIOSSafari();

  return (
    <section id="core-services" className="py-10 md:py-16 lg:py-18 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 標題 */}
        {isIOSSafari ? (
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-4">
              {title}
            </h2>
          </div>
        ) : (
          <ScrollTriggerMotion
            variants={titleScrollVariants}
            className="text-center mb-12 md:mb-16"
          >
            <h2 className="text-2xl md:text-4xl font-bold text-[#2b354d] mb-4">
              {title}
            </h2>
          </ScrollTriggerMotion>
        )}

        {/* 服務卡片網格 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {services.map((service, index) => (
            isIOSSafari ? (
              <div
                key={service.id}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
              >
                <ServiceCardContent service={service} />
              </div>
            ) : (
              <CardMotion
                key={service.id}
                index={index}
                variants={cardEntranceVariants}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
              >
                <ServiceCardContent service={service} />
              </CardMotion>
            )
          ))}
        </div>
      </div>
    </section>
  );
};

const ServiceCardContent = ({ service }: { service: ServiceCard }) => (
  <>
    {/* 圖片區域 */}
    <div className="relative h-64 overflow-hidden">
      <Image
        src={service.image}
        alt={service.title}
        fill
        className="object-cover group-hover:scale-110 transition-transform duration-500"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
      
      {/* 圖示 */}
      <div className="absolute top-4 left-4 p-3 bg-white/90 backdrop-blur-sm rounded-full shadow-lg">
        <div className="text-[#f59e0b] w-6 h-6">
          {service.icon}
        </div>
      </div>
    </div>

    {/* 內容區域 */}
    <div className="p-6 lg:p-8">
      <h3 className="text-lg md:text-xl font-bold text-[#2b354d] mb-3">
        {service.title}
      </h3>
      <p className="text-sm md:text-base text-gray-600 leading-relaxed mb-6">
        {service.description}
      </p>
      
      {/* CTA 按鈕 */}
      <Link
        href={service.link}
        className="inline-flex items-center text-[#2b354d] font-semibold hover:text-[#f59e0b] transition-colors duration-300 group/link cursor-pointer"
      >
        了解更多
        <ArrowRight className="ml-2 w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
      </Link>
    </div>
  </>
);

export default CoreServicesSection;
