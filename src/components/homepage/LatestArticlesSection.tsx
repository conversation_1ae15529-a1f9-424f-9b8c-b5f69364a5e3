"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

import { ScrollTriggerMotion, CardMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, cardEntranceVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";
import { ArrowRight } from "lucide-react";
import { BlogListItem, formatDate, extractTextFromHtml } from '@/types/blog';
import LazyImage from '@/components/LazyImage';

interface LatestArticlesSectionProps {
  title: string;
  ctaText: string;
  ctaLink: string;
  maxArticles?: number;
}

const LatestArticlesSection = ({ 
  title, 
  ctaText, 
  ctaLink, 
  maxArticles = 3 
}: LatestArticlesSectionProps) => {
  const [articles, setArticles] = useState<BlogListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const isIOSSafari = useIsIOSSafari();

  useEffect(() => {
    // 從真實 API 獲取文章
    const fetchArticles = async () => {
      try {
        const response = await fetch(`/api/blog?page=0&pageSize=${maxArticles}`);
        if (!response.ok) {
          throw new Error('Failed to fetch articles');
        }
        const data = await response.json();
        setArticles(data.posts || []);
      } catch (error) {
        console.error('Failed to fetch articles:', error);
        // 如果 API 失敗，使用空陣列
        setArticles([]);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, [maxArticles]);

  if (loading) {
    return (
      <section className="py-16 md:py-20 lg:py-24 bg-[#2b354d]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-12 bg-white/20 rounded-lg w-64 mx-auto mb-16"></div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-white/10 rounded-2xl h-96"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-10 md:py-16 lg:py-18 bg-[#2b354d]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 標題 */}
        {isIOSSafari ? (
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
              {title}
            </h2>
          </div>
        ) : (
          <ScrollTriggerMotion
            variants={titleScrollVariants}
            className="text-center mb-12 md:mb-16"
          >
            <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
              {title}
            </h2>
          </ScrollTriggerMotion>
        )}

        {/* 文章卡片網格 - 使用與 /blog 頁面相同的樣式 */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 blog-card-grid mb-12">
          {articles.map((article, index) => (
            isIOSSafari ? (
              <Link
                key={article.slug}
                href={`/blog/${article.slug}`}
                className="block h-full w-full"
              >
                <ArticleCardContent article={article} />
              </Link>
            ) : (
              <CardMotion
                key={article.slug}
                index={index}
                variants={cardEntranceVariants}
                as="div"
              >
                <Link
                  href={`/blog/${article.slug}`}
                  className="block h-full w-full"
                >
                  <ArticleCardContent article={article} />
                </Link>
              </CardMotion>
            )
          ))}
        </div>

        {/* CTA 按鈕 */}
        {isIOSSafari ? (
          <div className="text-center">
            <Link
              href={ctaLink}
              className="inline-flex items-center px-8 py-4 text-lg font-semibold text-[#2b354d] bg-white rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 cursor-pointer"
            >
              {ctaText}
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </div>
        ) : (
          <ScrollTriggerMotion
            variants={cardEntranceVariants}
            custom={articles.length}
            className="text-center"
          >
            <Link
              href={ctaLink}
              className="inline-flex items-center px-8 py-4 text-lg font-semibold text-[#2b354d] bg-white rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 cursor-pointer"
            >
              {ctaText}
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </ScrollTriggerMotion>
        )}
      </div>
    </section>
  );
};

const ArticleCardContent = ({ article }: { article: BlogListItem }) => {
  // 從 SEO 描述中提取摘要
  const excerpt = extractTextFromHtml(article.seoDescription, 120);

  // 確保 publishDate 是 Date 物件
  const publishDate = article.publishDate instanceof Date ? article.publishDate : new Date(article.publishDate);

  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border border-slate-200 blog-card h-full w-full flex flex-col">
      {/* 文章圖片 */}
      <div className="relative w-full h-64 overflow-hidden flex-shrink-0">
        <LazyImage
          src={article.thumbnail || '/images/placeholder-blog.svg'}
          alt={article.title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          placeholder="/images/placeholder-blog.svg"
        />
      </div>

      {/* 文章資訊 */}
      <div className="p-6 flex flex-col flex-grow min-w-0">
        {/* 發布日期 */}
        <div className="flex items-center text-sm mb-3 truncate" style={{ color: '#2b354d', opacity: 0.7 }}>
          <time dateTime={publishDate.toISOString()}>
            {formatDate(publishDate)}
          </time>
        </div>

        {/* 文章標題 */}
        <h3 className="text-xl font-semibold mb-3 line-clamp-2 leading-tight transition-colors min-w-0 break-words" style={{ color: '#2b354d' }}>
          {article.title}
        </h3>

        {/* 文章摘要 */}
        {excerpt && (
          <p className="line-clamp-3 leading-relaxed mt-auto min-w-0 break-words" style={{ color: '#2b354d', opacity: 0.8 }}>
            {excerpt}
          </p>
        )}
      </div>
    </div>
  );
};

export default LatestArticlesSection;
