"use client";

import Link from "next/link";
import Image from "next/image";

import { ScrollTriggerMotion } from "@/components/motion/MotionWrapper";
import { titleScrollVariants, contentScrollVariants } from "@/lib/motion-config";
import { useIsIOSSafari } from "@/hooks/useBrowserDetection";
import { ArrowRight, Heart, Target, Users } from "lucide-react";

interface BrandStorySectionProps {
  title: string;
  content: string[];
  ctaText: string;
  ctaLink: string;
  backgroundImage?: string;
  values?: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
  }>;
}

const BrandStorySection = ({
  title,
  content,
  ctaText,
  ctaLink,
  backgroundImage,
  values = []
}: BrandStorySectionProps) => {
  const isIOSSafari = useIsIOSSafari();

  const defaultValues = [
    {
      icon: <Heart className="w-6 h-6" />,
      title: "用心服務",
      description: "以專業與熱忱為每位錶友提供最優質的服務體驗"
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "精準專業",
      description: "結合傳統工藝與現代科技，追求完美的精準度"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "信任夥伴",
      description: "成為錶友們最值得信賴的腕錶管家與顧問"
    }
  ];

  const displayValues = values.length > 0 ? values : defaultValues;

  return (
    <section className="py-10 md:py-16 lg:py-18 bg-[#2b354d] relative overflow-hidden">
      {/* 背景圖片 */}
      {backgroundImage && (
        <div className="absolute inset-0">
          <Image
            src={backgroundImage}
            alt="品牌故事背景"
            fill
            className="object-cover opacity-10"
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-[#2b354d]/80" />
        </div>
      )}

      {/* 裝飾元素 */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-[#f59e0b]/10 to-transparent rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-[#f59e0b]/5 to-transparent rounded-full blur-3xl" />

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 標題 */}
        {isIOSSafari ? (
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-white mb-8">
              {title}
            </h2>
          </div>
        ) : (
          <ScrollTriggerMotion
            variants={titleScrollVariants}
            className="text-center mb-12 md:mb-16"
          >
            <h2 className="text-2xl md:text-4xl font-bold text-white mb-8">
              {title}
            </h2>
          </ScrollTriggerMotion>
        )}

        {/* 內容段落 */}
        <div className="max-w-4xl mx-auto mb-16">
          {content.map((paragraph, index) => (
            isIOSSafari ? (
              <p
                key={index}
                className="text-base md:text-lg text-white/90 leading-relaxed mb-6 text-center"
              >
                {paragraph}
              </p>
            ) : (
              <ScrollTriggerMotion
                key={index}
                variants={contentScrollVariants}
                delay={0.2 + index * 0.1}
                className="mb-6"
              >
                <p className="text-base md:text-lg text-white/90 leading-relaxed text-center">
                  {paragraph}
                </p>
              </ScrollTriggerMotion>
            )
          ))}
        </div>

        {/* 品牌價值 */}
        {displayValues.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {displayValues.map((value, index) => (
              isIOSSafari ? (
                <div
                  key={index}
                  className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-[#f59e0b]/20 rounded-full mb-4">
                    <div className="text-[#f59e0b]">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">
                    {value.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              ) : (
                <ScrollTriggerMotion
                  key={index}
                  variants={contentScrollVariants}
                  delay={0.4 + index * 0.1}
                  className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-[#f59e0b]/20 rounded-full mb-4">
                    <div className="text-[#f59e0b]">
                      {value.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-3">
                    {value.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed">
                    {value.description}
                  </p>
                </ScrollTriggerMotion>
              )
            ))}
          </div>
        )}

        {/* CTA 按鈕 */}
        {isIOSSafari ? (
          <div className="text-center">
            <Link
              href={ctaLink}
              className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group cursor-pointer"
            >
              {ctaText}
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        ) : (
          <ScrollTriggerMotion
            variants={contentScrollVariants}
            delay={0.8}
            className="text-center"
          >
            <Link
              href={ctaLink}
              className="inline-flex items-center px-6 py-4 text-base font-semibold text-[#2b354d] bg-white rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 group cursor-pointer"
            >
              {ctaText}
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </ScrollTriggerMotion>
        )}
      </div>
    </section>
  );
};

export default BrandStorySection;
