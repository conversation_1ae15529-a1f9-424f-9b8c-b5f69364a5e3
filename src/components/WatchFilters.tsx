'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PRICE_RANGES, SORT_OPTIONS, type PriceRange, type SortOption, type WatchFilters } from '@/types/watch';

interface WatchFiltersProps {
  brands: string[];
  filters: WatchFilters;
  onFiltersChange: (filters: WatchFilters) => void;
}

const WatchFilters = ({ brands, filters, onFiltersChange }: WatchFiltersProps) => {
  const handleBrandChange = (brand: string) => {
    onFiltersChange({ ...filters, brand });
  };

  const handlePriceRangeChange = (priceRange: PriceRange) => {
    onFiltersChange({ ...filters, priceRange });
  };

  const handleSortChange = (sortBy: SortOption) => {
    onFiltersChange({ ...filters, sortBy });
  };

  return (
    <div className="pt-8 pb-4">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 篩選和排序控制項 */}
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 sm:justify-between">
          {/* 左側篩選區域 */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
            {/* 品牌篩選下拉選單 */}
            <div className="flex-1 sm:flex-none sm:min-w-[200px]">
              <Select value={filters.brand} onValueChange={handleBrandChange}>
                <SelectTrigger className="w-full" style={{ color: '#2b354d' }}>
                  <SelectValue placeholder="選擇品牌">
                    {filters.brand === 'all' ? '全部品牌' : filters.brand}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部品牌</SelectItem>
                  {brands.map((brand) => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 價格篩選下拉選單 */}
            <div className="flex-1 sm:flex-none sm:min-w-[220px]">
              <Select value={filters.priceRange} onValueChange={handlePriceRangeChange}>
                <SelectTrigger className="w-full" style={{ color: '#2b354d' }}>
                  <SelectValue placeholder="選擇價格範圍">
                    {PRICE_RANGES[filters.priceRange].label}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(PRICE_RANGES).map(([key, range]) => (
                    <SelectItem key={key} value={key}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 右側排序區域 */}
          <div className="flex-1 sm:flex-none sm:min-w-[180px]">
            <Select value={filters.sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-full" style={{ color: '#2b354d' }}>
                <SelectValue placeholder="選擇排序方式">
                  {SORT_OPTIONS[filters.sortBy].label}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.entries(SORT_OPTIONS).map(([key, option]) => (
                  <SelectItem key={key} value={key}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WatchFilters;
