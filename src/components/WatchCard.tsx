import Link from 'next/link';
import LazyImage from './LazyImage';
import { WatchListItem } from '@/types/watch';

interface WatchCardProps {
  watch: WatchListItem;
}

const WatchCard = ({ watch }: WatchCardProps) => {
  // 格式化價格顯示
  const formatPrice = (price: number) => {
    return `NT$ ${new Intl.NumberFormat('zh-TW').format(price)}`;
  };

  // 檢查是否已成交
  const isSold = watch.availability === '已成交';

  // 決定是否可點擊
  const isClickable = !isSold;

  // 卡片內容
  const cardContent = (
    <div className={`bg-white rounded-2xl overflow-hidden shadow-lg transition-all duration-300 border border-slate-200 watch-card h-full w-full flex flex-col ${
      isClickable ? 'hover:shadow-xl group' : 'opacity-60 cursor-not-allowed'
    } ${isSold ? 'bg-gray-100' : ''}`}>
      {/* 手錶圖片 */}
      <div className="relative w-full h-64 overflow-hidden flex-shrink-0">
        <LazyImage
          src={watch.thumbnail || '/images/placeholder-watch.svg'}
          alt={watch.productName}
          fill
          className={`object-cover transition-transform duration-300 ${
            isClickable ? 'group-hover:scale-105' : ''
          }`}
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          placeholder="/images/placeholder-watch.svg"
        />

        {/* Tag 標籤 - 顯示在圖片左上角 */}
        {watch.tag && watch.tag.trim() !== '' && (
          <div className="absolute top-3 left-3 z-10">
            <span className="inline-block px-3 py-1 text-sm font-semibold text-white rounded-full shadow-lg" style={{ backgroundColor: '#f59e0b' }}>
              {watch.tag}
            </span>
          </div>
        )}

        {/* Availability 狀態標籤 */}
        {watch.availability && watch.availability !== '銷售中' && (
          <div className="absolute top-3 right-3 z-10">
            <span className={`inline-block px-3 py-1 text-sm font-semibold rounded-full shadow-lg ${
              watch.availability === '已成交'
                ? 'text-gray-600 bg-gray-200'
                : 'text-white'
            }`} style={{
              backgroundColor: watch.availability === '已預約' ? '#2b354d' : undefined
            }}>
              {watch.availability}
            </span>
          </div>
        )}
      </div>

      {/* 手錶資訊 */}
      <div className="p-4 sm:p-6 flex flex-col flex-grow min-w-0">
        {/* 品牌 */}
        <p className={`text-sm font-medium uppercase tracking-wide mb-2 truncate ${
          isSold ? 'text-gray-500' : 'text-[#2b354d] opacity-70'
        }`}>
          {watch.brand}
        </p>

        {/* 產品名稱 */}
        <h3 className={`text-base sm:text-lg font-semibold mb-3 line-clamp-2 leading-tight flex-grow min-w-0 break-words ${
          isSold ? 'text-gray-600' : 'text-[#2b354d]'
        }`}>
          {watch.productName}
        </h3>

        {/* 價格 */}
        <p className={`text-lg sm:text-xl font-bold mt-auto truncate ${
          isSold ? 'text-gray-500' : 'text-[#2b354d]'
        }`}>
          {formatPrice(watch.price)}
        </p>
      </div>
    </div>
  );

  // 根據是否可點擊返回不同的組件
  return isClickable ? (
    <Link href={`/pre-owned/${watch.seoSlug || watch.id}`} className="block h-full w-full">
      {cardContent}
    </Link>
  ) : (
    <div className="block h-full w-full">
      {cardContent}
    </div>
  );
};

export default WatchCard;
