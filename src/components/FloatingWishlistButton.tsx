'use client';

import { useState } from 'react';
import { Heart } from 'lucide-react';
import { useWishlist } from '@/hooks/useWishlist';

interface FloatingWishlistButtonProps {
  onOpenWishlist: () => void;
}

export default function FloatingWishlistButton({ onOpenWishlist }: FloatingWishlistButtonProps) {
  const { wishlistCount, isLoaded } = useWishlist();
  const [isHovered, setIsHovered] = useState(false);

  // 如果還沒載入或清單為空，不顯示按鈕
  if (!isLoaded || wishlistCount === 0) {
    return null;
  }

  return (
    <div className="fixed right-4 bottom-20 z-50">
      <button
        onClick={onOpenWishlist}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="relative flex items-center justify-center w-14 h-14 rounded-full shadow-lg transition-all duration-300 hover:scale-110 cursor-pointer"
        style={{
          backgroundColor: '#2b354d',
          color: '#ffffff'
        }}
        aria-label={`鑑賞清單 (${wishlistCount} 件)`}
      >
        <Heart className="w-6 h-6" />
        
        {/* 數量徽章 */}
        <div 
          className="absolute -top-2 -right-2 flex items-center justify-center w-6 h-6 text-xs font-bold rounded-full"
          style={{
            backgroundColor: '#f59e0b',
            color: '#ffffff'
          }}
        >
          {wishlistCount > 99 ? '99+' : wishlistCount}
        </div>

        {/* Hover 提示 */}
        {isHovered && (
          <div 
            className="absolute right-full mr-3 px-3 py-2 text-sm whitespace-nowrap rounded-lg shadow-lg"
            style={{
              backgroundColor: '#2b354d',
              color: '#ffffff'
            }}
          >
            我的鑑賞清單 ({wishlistCount} 件)
            <div 
              className="absolute top-1/2 left-full w-0 h-0 -translate-y-1/2"
              style={{
                borderLeft: '6px solid #2b354d',
                borderTop: '6px solid transparent',
                borderBottom: '6px solid transparent'
              }}
            />
          </div>
        )}
      </button>
    </div>
  );
}
