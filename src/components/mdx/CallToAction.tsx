import React from 'react';
import Link from 'next/link';

interface CallToActionProps {
  title: string;
  description?: string;
  buttonText: string;
  buttonUrl: string;
  variant?: 'primary' | 'secondary';
  external?: boolean;
}

export default function CallToAction({
  buttonText,
  buttonUrl,
  external = false
}: Pick<CallToActionProps, 'buttonText' | 'buttonUrl' | 'external'>) {
  const ButtonComponent = external ? 'a' : Link;
  const buttonProps = external
    ? { href: buttonUrl, target: '_blank', rel: 'noopener noreferrer' }
    : { href: buttonUrl };

  return (
    <div className="my-8">
      <ButtonComponent
        {...buttonProps}
        className="block w-full py-4 text-center text-base font-semibold rounded-lg transition-all duration-200 hover:opacity-90 no-underline"
        style={{ backgroundColor: '#2b354d', color: 'white', textDecoration: 'none' }}
      >
        {buttonText}
      </ButtonComponent>
    </div>
  );
}
