import React from 'react';
import Image from 'next/image';

interface ImageWithCaptionProps {
  src: string;
  alt: string;
  caption?: string;
  width?: number;
  className?: string;
}

export default function ImageWithCaption({
  src,
  alt,
  caption,
  width = 1200,
  className = ''
}: ImageWithCaptionProps) {
  return (
    <figure className={`my-6 ${className}`}>
      <div className="relative w-full overflow-hidden rounded-lg">
        <Image
          src={src}
          alt={alt}
          width={width}
          height={0}
          className="w-full h-auto object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
          quality={90}
        />
      </div>
      {caption && (
        <figcaption
          className="mt-1 text-sm text-slate-600 text-left"
          dangerouslySetInnerHTML={{ __html: caption }}
        />
      )}
    </figure>
  );
}
