import Link from 'next/link';
import Image from 'next/image';
import { getServerBlogPostEdge } from '@/lib/blog-server-edge';
import { notFound } from 'next/navigation';
import { formatDate } from '@/types/blog';

interface BlogDetailContentProps {
  slug: string;
}

export default async function BlogDetailContentEdge({ slug }: BlogDetailContentProps) {
  const post = await getServerBlogPostEdge(slug);

  if (!post) {
    notFound();
  }

  // 確保 publishDate 是 Date 物件
  const publishDate = post.publishDate instanceof Date ? post.publishDate : new Date(post.publishDate);

  return (
    <div className="min-h-screen bg-white">
      {/* 返回按鈕 */}
      <div className="border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-slate-600 hover:text-slate-800 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            返回
          </Link>
        </div>
      </div>

      {/* 文章內容 */}
      <article className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 文章標題 */}
          <header className="mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
              {post.title}
            </h1>

            {/* 文章資訊 */}
            <div className="flex items-center text-slate-600 mb-8">
              <time dateTime={publishDate.toISOString()}>
                {formatDate(publishDate)}
              </time>
            </div>
          </header>

          {/* Hero 圖片區域 */}
          {post.hero && (
            <div className="mb-12">
              {/* 檢查是否為 URL 或 HTML */}
              {post.hero.startsWith('http') ? (
                <div className="relative w-full h-96">
                  <Image
                    src={post.hero}
                    alt={post.title}
                    fill
                    className="rounded-xl object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                    priority
                    quality={90}
                  />
                </div>
              ) : (
                <div
                  className="prose prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: post.hero }}
                />
              )}
            </div>
          )}

          {/* 文章內容 - 移除卡片容器和背景色 */}
          <div className="prose prose-lg max-w-none mb-8">
            <div
              dangerouslySetInnerHTML={{ __html: post.body }}
            />
          </div>

          {/* 作者介紹區塊 */}
          <div className="pt-4">
            <div className="prose prose-lg max-w-none">
              <h2 className="text-2xl font-bold mb-4" style={{ color: '#2b354d' }}>作者介紹</h2>
              <p className="mb-4 leading-relaxed" style={{ color: '#2b354d' }}>
                Weaven 是一個專注於機械錶相關產品服務的台灣團隊，致力於「讓享受機械錶變簡單」，產品包括：Pangea 機械錶智慧收藏盒、AQUA PURE 瑠璃冰河清潔組，亦不定期舉辦錶匠體驗活動，讓大家有機會體驗機械錶精細與奧妙。
              </p>
              <p style={{ color: '#2b354d' }}>
                這裡可以找到我們：
                <a
                  href="https://www.facebook.com/weaven.co/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors mx-1"
                >
                  Facebook
                </a>
                |
                <a
                  href="https://www.instagram.com/weaven2019/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 transition-colors mx-1"
                >
                  Instagram
                </a>
              </p>
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}
