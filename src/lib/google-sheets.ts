import { google } from 'googleapis';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

// 範圍：允許讀寫 Google Sheets
const SCOPES = ['https://www.googleapis.com/auth/spreadsheets'];



/**
 * 取得經過授權的 Google Sheets API 客戶端
 */
export function getSheetsClient() {
  const serviceAccountEmail = GOOGLE_SHEETS_CONFIG.getServiceAccountEmail();
  const privateKey = GOOGLE_SHEETS_CONFIG.getPrivateKey();

  if (!serviceAccountEmail || !privateKey) {
    throw new Error('缺少 Google Service Account 憑證的環境變數');
  }

  const auth = new google.auth.GoogleAuth({
    credentials: {
      client_email: serviceAccountEmail,
      private_key: privateKey,
    },
    scopes: SCOPES,
  });

  // The 'auth' object itself is sufficient for authentication.
  return google.sheets({ version: 'v4', auth });
}

/**
 * 將資料附加到指定的 Google Sheet 中
 * @param range - 要附加資料的範圍，例如 '報名紀錄!A1'
 * @param values - 要附加的二維陣列資料，例如 [['值1', '值2']]
 */
export async function appendToSheet(range: string, values: (string | number | boolean | null)[][]) {
  try {
    const sheets = getSheetsClient(); // Changed: No longer async
    const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();

    if (!sheetId) {
      throw new Error('Google Sheet ID 未設定');
    }

    const response = await sheets.spreadsheets.values.append({
      spreadsheetId: sheetId,
      range,
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values,
      },
    });
    return response.data;
  } catch (error) {
    console.error('寫入 Google Sheet 失敗:', error);
    throw new Error('無法寫入 Google Sheet');
  }
}

/**
 * 從指定的 Google Sheet 讀取資料
 * @param range - 要讀取的範圍，例如 '活動列表!A:D'
 */
export async function getSheetData(range: string) {
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();
  if (!sheetId) {
    throw new Error('Google Sheet ID 未設定');
  }

  try {
    console.log(`📡 從 Google Sheets 讀取資料: ${range}`);
    const sheets = getSheetsClient();
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetId,
      range,
    });

    const data = response.data.values || [];
    return data;
  } catch (error) {
    console.error('讀取 Google Sheet 失敗:', error);
    throw new Error('無法讀取 Google Sheet');
  }
}

/**
 * 從手錶專用的 Google Sheet 讀取資料
 * @param range - 要讀取的範圍，例如 '手錶庫存!A:O'
 */
export async function getWatchSheetData(range: string) {
  const sheetId = GOOGLE_SHEETS_CONFIG.getWatchSheetId();
  if (!sheetId) {
    throw new Error('手錶 Google Sheet ID 未設定');
  }

  try {
    console.log(`📡 從手錶 Google Sheets 讀取資料: ${range}`);
    const sheets = getSheetsClient();
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetId,
      range,
    });

    const data = response.data.values || [];
    return data;
  } catch (error) {
    console.error('讀取手錶 Google Sheet 失敗:', error);
    throw new Error('無法讀取手錶 Google Sheet');
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取資料
 * @param range - 要讀取的範圍，例如 '部落格文章!A:F'
 */
export async function getBlogSheetData(range: string) {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('未設定部落格 Google Sheets ID，請檢查環境變數配置');
  }

  try {
    console.log(`📡 從部落格 Google Sheets 讀取資料: ${range}`);
    const sheets = getSheetsClient();
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetId,
      range,
    });

    const data = response.data.values || [];
    return data;
  } catch (error) {
    console.error('讀取部落格 Google Sheet 失敗:', error);

    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤: ${range}。請確認工作表名稱 "Blog" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表: ${sheetId}。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取部落格 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取文章列表資料（優化版本，只讀取必要欄位）
 */
export async function getBlogListSheetData() {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('未設定部落格 Google Sheets ID，請檢查環境變數配置');
  }

  // 只讀取文章列表需要的欄位：A(title), D(author), E(thumbnail), F(time), G(seoSlug), I(seoDescription)
  const POSSIBLE_BLOG_LIST_RANGES = [
    'Blog!A:A,Blog!D:D,Blog!E:F,Blog!G:G,Blog!I:I',     // 英文名稱，分別讀取需要的欄位
    '部落格文章!A:A,部落格文章!D:D,部落格文章!E:F,部落格文章!G:G,部落格文章!I:I', // 中文名稱
    'Sheet1!A:A,Sheet1!D:D,Sheet1!E:F,Sheet1!G:G,Sheet1!I:I',   // 預設名稱
    'A:A,D:D,E:F,G:G,I:I'                               // 不指定工作表
  ];

  try {
    console.log(`📡 從部落格 Google Sheets 讀取列表資料（優化版本）`);
    const sheets = getSheetsClient();

    // 嘗試使用批次讀取來獲取特定欄位
    for (const ranges of POSSIBLE_BLOG_LIST_RANGES) {
      try {
        const rangeArray = ranges.split(',');
        const response = await sheets.spreadsheets.values.batchGet({
          spreadsheetId: sheetId,
          ranges: rangeArray,
        });

        if (response.data.valueRanges && response.data.valueRanges.length > 0) {
          // 重組資料：將分散的欄位合併成完整的行
          const valueRanges = response.data.valueRanges;
          const maxRows = Math.max(...valueRanges.map(vr => (vr.values || []).length));

          const combinedData: string[][] = [];
          for (let i = 0; i < maxRows; i++) {
            const row: string[] = [];
            // A欄 (title)
            row[0] = valueRanges[0]?.values?.[i]?.[0] || '';
            // D欄 (author)
            row[1] = valueRanges[1]?.values?.[i]?.[0] || '';
            // E欄 (thumbnail)
            row[2] = valueRanges[2]?.values?.[i]?.[0] || '';
            // F欄 (time)
            row[3] = valueRanges[2]?.values?.[i]?.[1] || '';
            // G欄 (seoSlug)
            row[4] = valueRanges[3]?.values?.[i]?.[0] || '';
            // I欄 (seoDescription)
            row[5] = valueRanges[4]?.values?.[i]?.[0] || '';

            combinedData.push(row);
          }

          return combinedData;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${ranges} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取部落格列表 Google Sheet 失敗:', error);

    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤。請確認工作表名稱 "Blog" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表: ${sheetId}。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取部落格列表 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從部落格專用的 Google Sheet 讀取單篇文章詳情資料（優化版本）
 * 只在需要時讀取完整的文章內容
 */
export async function getBlogDetailSheetData(slug: string) {
  const sheetId = GOOGLE_SHEETS_CONFIG.getBlogSheetId();
  if (!sheetId) {
    throw new Error('未設定部落格 Google Sheets ID，請檢查環境變數配置');
  }

  // 先嘗試讀取所有資料，然後在應用層過濾
  // 這樣可以避免多次 API 呼叫
  const POSSIBLE_BLOG_RANGES = [
    'Blog!A:L',           // 英文名稱
    '部落格文章!A:L',      // 中文名稱
    'Sheet1!A:L',         // 預設名稱
    'A:L'                 // 不指定工作表
  ];

  try {
    console.log(`📡 從部落格 Google Sheets 讀取文章詳情: ${slug}`);
    const sheets = getSheetsClient();

    // 嘗試不同的範圍
    for (const range of POSSIBLE_BLOG_RANGES) {
      try {
        const response = await sheets.spreadsheets.values.get({
          spreadsheetId: sheetId,
          range,
        });

        if (response.data.values && response.data.values.length > 0) {
          return response.data.values;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${range} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取部落格詳情 Google Sheet 失敗:', error);
    throw new Error(`無法讀取部落格詳情 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從手錶專用的 Google Sheet 讀取手錶列表資料（優化版本，只讀取必要欄位）
 * 新的欄位順序：A~F + M (Product Name, Brand, Price, Thumbnail, Tag, Availability, SEO:Slug)
 */
export async function getWatchListSheetData() {
  const sheetId = GOOGLE_SHEETS_CONFIG.getWatchSheetId();
  if (!sheetId) {
    throw new Error('未設定手錶 Google Sheets ID，請檢查環境變數配置');
  }

  // 只讀取手錶列表需要的欄位：A(productName), B(brand), C(price), D(thumbnail), E(tag), F(availability), M(seoSlug)
  const POSSIBLE_WATCH_LIST_RANGES = [
    '手錶庫存!A:F,手錶庫存!M:M',     // 中文名稱，讀取 A~F 和 M 欄位
    'Watches!A:F,Watches!M:M',       // 英文名稱
    'Sheet1!A:F,Sheet1!M:M',         // 預設名稱
    'A:F,M:M'                        // 不指定工作表
  ];

  try {
    console.log(`📡 從手錶 Google Sheets 讀取列表資料（優化版本）`);
    const sheets = getSheetsClient();

    // 嘗試使用批次讀取來獲取特定欄位
    for (const ranges of POSSIBLE_WATCH_LIST_RANGES) {
      try {
        const rangeArray = ranges.split(',');
        const response = await sheets.spreadsheets.values.batchGet({
          spreadsheetId: sheetId,
          ranges: rangeArray,
        });

        if (response.data.valueRanges && response.data.valueRanges.length > 0) {
          // 重組資料：將分散的欄位合併成完整的行
          const valueRanges = response.data.valueRanges;
          const maxRows = Math.max(...valueRanges.map(vr => (vr.values || []).length));

          const combinedData: string[][] = [];
          for (let i = 0; i < maxRows; i++) {
            const row: string[] = [];
            // A~F 欄位 (productName, brand, price, thumbnail, tag, availability)
            for (let j = 0; j < 6; j++) {
              row[j] = valueRanges[0]?.values?.[i]?.[j] || '';
            }
            // 跳過 G~L 欄位（在 row 中保持空位）
            for (let j = 6; j < 12; j++) {
              row[j] = '';
            }
            // M 欄位 (seoSlug)
            row[12] = valueRanges[1]?.values?.[i]?.[0] || '';

            combinedData.push(row);
          }

          return combinedData;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${ranges} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取手錶列表 Google Sheet 失敗:', error);

    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤。請確認工作表名稱 "手錶庫存" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表: ${sheetId}。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取手錶列表 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從常見問題專用的 Google Sheet 讀取問題列表資料（優化版本，只讀取必要欄位）
 */
export async function getFAQListSheetData() {
  const sheetId = process.env.GOOGLE_QUESTION_SHEET_ID || process.env.GOOGLE_SHEET_ID;
  if (!sheetId) {
    throw new Error('未設定 GOOGLE_SHEET_ID 或 GOOGLE_QUESTION_SHEET_ID 環境變數');
  }

  // 只讀取常見問題列表需要的欄位：A(question), C(tag)
  const POSSIBLE_FAQ_LIST_RANGES = [
    '常見問題!A:A,常見問題!C:C',     // 中文名稱，分別讀取需要的欄位
    'FAQ!A:A,FAQ!C:C',             // 英文名稱
    'Sheet1!A:A,Sheet1!C:C',       // 預設名稱
    'A:A,C:C'                      // 不指定工作表
  ];

  try {
    console.log(`📡 從常見問題 Google Sheets 讀取列表資料（優化版本）`);
    const sheets = getSheetsClient();

    // 嘗試使用批次讀取來獲取特定欄位
    for (const ranges of POSSIBLE_FAQ_LIST_RANGES) {
      try {
        const rangeArray = ranges.split(',');
        const response = await sheets.spreadsheets.values.batchGet({
          spreadsheetId: sheetId,
          ranges: rangeArray,
        });

        if (response.data.valueRanges && response.data.valueRanges.length > 0) {
          // 重組資料：將分散的欄位合併成完整的行
          const valueRanges = response.data.valueRanges;
          const maxRows = Math.max(...valueRanges.map(vr => (vr.values || []).length));

          const combinedData: string[][] = [];
          for (let i = 0; i < maxRows; i++) {
            const row: string[] = [];
            // A欄 (question)
            row[0] = valueRanges[0]?.values?.[i]?.[0] || '';
            // C欄 (tag)
            row[1] = valueRanges[1]?.values?.[i]?.[0] || '';

            combinedData.push(row);
          }

          return combinedData;
        }
      } catch {
        // 繼續嘗試下一個範圍
        console.log(`嘗試範圍 ${ranges} 失敗，繼續下一個...`);
      }
    }

    throw new Error('所有範圍都無法讀取');

  } catch (error) {
    console.error('讀取常見問題列表 Google Sheet 失敗:', error);

    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤。請確認工作表名稱 "常見問題" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表: ${sheetId}。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取常見問題列表 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 從常見問題專用的 Google Sheet 讀取資料
 * @param range - 要讀取的範圍，例如 '常見問題!A:C'
 */
export async function getFAQSheetData(range: string) {
  const sheetId = process.env.GOOGLE_QUESTION_SHEET_ID || process.env.GOOGLE_SHEET_ID;
  if (!sheetId) {
    throw new Error('未設定 GOOGLE_SHEET_ID 或 GOOGLE_QUESTION_SHEET_ID 環境變數');
  }

  try {
    console.log(`📡 從常見問題 Google Sheets 讀取資料: ${range}`);
    const sheets = getSheetsClient();
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetId,
      range,
    });

    const data = response.data.values || [];
    return data;
  } catch (error) {
    console.error('讀取常見問題 Google Sheet 失敗:', error);

    if (error instanceof Error) {
      // 提供更具體的錯誤訊息
      if (error.message.includes('Unable to parse range')) {
        throw new Error(`Google Sheets 範圍格式錯誤: ${range}。請確認工作表名稱 "常見問題" 是否存在。`);
      } else if (error.message.includes('not found')) {
        throw new Error(`找不到 Google Sheets 或工作表: ${sheetId}。請檢查 Sheet ID 和工作表名稱。`);
      } else if (error.message.includes('permission')) {
        throw new Error('Google Sheets 權限不足。請確認服務帳戶有存取權限。');
      }
    }

    throw new Error(`無法讀取常見問題 Google Sheet: ${error instanceof Error ? error.message : String(error)}`);
  }
}
