/**
 * Edge Runtime 兼容的 Google Sheets 客戶端
 * 使用 Web Crypto API 和 fetch，不依賴 Node.js 模組
 */

import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';

// Google Sheets API 基礎 URL
const SHEETS_API_BASE = 'https://sheets.googleapis.com/v4/spreadsheets';
const AUTH_URL = 'https://oauth2.googleapis.com/token';

/**
 * 使用 Web Crypto API 創建 JWT
 */
async function createJWT(serviceAccountEmail: string, privateKey: string): Promise<string> {
  const now = Math.floor(Date.now() / 1000);
  const header = {
    alg: 'RS256',
    typ: 'JWT'
  };

  const payload = {
    iss: serviceAccountEmail,
    scope: 'https://www.googleapis.com/auth/spreadsheets',
    aud: AUTH_URL,
    exp: now + 3600, // 1 hour
    iat: now
  };

  // Base64URL 編碼
  const base64UrlEncode = (obj: Record<string, unknown>): string => {
    return btoa(JSON.stringify(obj))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  };

  const encodedHeader = base64UrlEncode(header);
  const encodedPayload = base64UrlEncode(payload);
  const unsignedToken = `${encodedHeader}.${encodedPayload}`;

  // 準備私鑰
  const pemHeader = '-----BEGIN PRIVATE KEY-----';
  const pemFooter = '-----END PRIVATE KEY-----';
  const pemContents = privateKey
    .replace(pemHeader, '')
    .replace(pemFooter, '')
    .replace(/\s/g, '');

  // 將 base64 轉換為 ArrayBuffer
  const binaryDer = Uint8Array.from(atob(pemContents), c => c.charCodeAt(0));

  // 導入私鑰
  const cryptoKey = await crypto.subtle.importKey(
    'pkcs8',
    binaryDer,
    {
      name: 'RSASSA-PKCS1-v1_5',
      hash: 'SHA-256'
    },
    false,
    ['sign']
  );

  // 簽名
  const encoder = new TextEncoder();
  const data = encoder.encode(unsignedToken);
  const signature = await crypto.subtle.sign('RSASSA-PKCS1-v1_5', cryptoKey, data);

  // Base64URL 編碼簽名
  const signatureArray = new Uint8Array(signature);
  const signatureBase64 = btoa(String.fromCharCode(...signatureArray))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return `${unsignedToken}.${signatureBase64}`;
}

/**
 * 獲取 Google API 訪問令牌
 */
async function getAccessToken(): Promise<string> {
  const serviceAccountEmail = GOOGLE_SHEETS_CONFIG.getServiceAccountEmail();
  const privateKey = GOOGLE_SHEETS_CONFIG.getPrivateKey();

  if (!serviceAccountEmail || !privateKey) {
    throw new Error('缺少 Google Service Account 憑證的環境變數');
  }

  try {
    const jwt = await createJWT(serviceAccountEmail, privateKey);

    const response = await fetch(AUTH_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: jwt,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`認證失敗: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error('Google API 認證失敗:', error);
    throw new Error(`Google API 認證失敗: ${error instanceof Error ? error.message : '未知錯誤'}`);
  }
}

/**
 * 讀取 Google Sheets 資料
 */
export async function getSheetDataEdge(
  sheetId: string,
  range: string
): Promise<string[][]> {
  try {
    console.log(`📡 從 Google Sheets 讀取資料: ${range}`);
    
    const accessToken = await getAccessToken();
    const url = `${SHEETS_API_BASE}/${sheetId}/values/${encodeURIComponent(range)}`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API 請求失敗: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    return data.values || [];
  } catch (error) {
    console.error('讀取手錶 Google Sheet 失敗:', error);
    throw error;
  }
}

/**
 * 寫入 Google Sheets 資料
 */
export async function appendSheetDataEdge(
  sheetId: string,
  range: string,
  values: string[][]
): Promise<void> {
  try {
    const accessToken = await getAccessToken();
    const url = `${SHEETS_API_BASE}/${sheetId}/values/${encodeURIComponent(range)}:append?valueInputOption=RAW`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        values,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`寫入失敗: ${response.status} ${errorText}`);
    }
  } catch (error) {
    console.error('寫入 Google Sheet 失敗:', error);
    throw error;
  }
}

/**
 * 更新 Google Sheets 資料
 */
export async function updateSheetDataEdge(
  sheetId: string,
  range: string,
  values: string[][]
): Promise<void> {
  try {
    const accessToken = await getAccessToken();
    const url = `${SHEETS_API_BASE}/${sheetId}/values/${encodeURIComponent(range)}?valueInputOption=RAW`;

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        values,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`更新失敗: ${response.status} ${errorText}`);
    }
  } catch (error) {
    console.error('更新 Google Sheet 失敗:', error);
    throw error;
  }
}

/**
 * 批量更新 Google Sheets 資料
 */
export async function batchUpdateSheetDataEdge(
  sheetId: string,
  updates: Array<{
    range: string;
    values: string[][];
  }>
): Promise<void> {
  try {
    const accessToken = await getAccessToken();
    const url = `${SHEETS_API_BASE}/${sheetId}/values:batchUpdate`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        valueInputOption: 'RAW',
        data: updates.map(update => ({
          range: update.range,
          values: update.values,
        })),
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`批量更新失敗: ${response.status} ${errorText}`);
    }
  } catch (error) {
    console.error('批量更新 Google Sheet 失敗:', error);
    throw error;
  }
}

/**
 * 兼容性函數：模擬原有的 getSheetsClient 行為
 */
export const EdgeSheetsClient = {
  async getSheetData(sheetId: string, range: string) {
    return getSheetDataEdge(sheetId, range);
  },
  
  async appendSheetData(sheetId: string, range: string, values: string[][]) {
    return appendSheetDataEdge(sheetId, range, values);
  },

  async updateSheetData(sheetId: string, range: string, values: string[][]) {
    return updateSheetDataEdge(sheetId, range, values);
  },

  async batchUpdateSheetData(sheetId: string, updates: Array<{ range: string; values: string[][] }>) {
    return batchUpdateSheetDataEdge(sheetId, updates);
  }
};
