// 伺服器端手錶資料獲取函數
// 用於 generateMetadata 和伺服器組件

import { getSheetDataEdge } from '@/lib/google-sheets-edge';
import { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';
import { transformWatchData, type Watch } from '@/types/watch';

// 手錶資料的 Google Sheets 範圍（修正為實際使用的 A:M）
const POSSIBLE_WATCH_RANGES = [
  '手錶庫存!A:M',       // 中文名稱（包含所有欄位：A~M）
  'Watches!A:M',        // 英文名稱
  'Sheet1!A:M',         // 預設名稱
  'A:M'                 // 不指定工作表
];

// 嘗試讀取資料的輔助函數
async function tryReadWatchData(): Promise<string[][] | null> {
  const sheetId = GOOGLE_SHEETS_CONFIG.getSheetId();

  for (const range of POSSIBLE_WATCH_RANGES) {
    try {
      const data = await getSheetDataEdge(sheetId, range);
      if (data && data.length > 0) {
        return data;
      }
    } catch {
      // 繼續嘗試下一個範圍
    }
  }

  return null;
}

// 伺服器端獲取單支手錶
export async function getServerWatch(id: string): Promise<Watch | null> {
  try {
    const rawData = await tryReadWatchData();
    
    if (!rawData || rawData.length === 0) {
      console.error('無法讀取手錶資料');
      return null;
    }

    // 轉換資料 - 遍歷所有行並轉換
    const watches: Watch[] = [];
    for (let i = 1; i < rawData.length; i++) { // 跳過標題行
      try {
        const watch = transformWatchData(rawData[i], i);
        watches.push(watch);
      } catch (error) {
        console.warn(`轉換第 ${i} 行資料失敗:`, error);
      }
    }

    // 尋找指定的手錶
    const watch = watches.find(w => w.id === id || w.seoSlug === id);
    
    if (!watch) {
      console.error(`找不到手錶: ${id}`);
      return null;
    }

    return watch;
  } catch (error) {
    console.error('獲取手錶詳情失敗:', error);
    return null;
  }
}

// 伺服器端獲取所有手錶（用於 sitemap 等）
export async function getServerWatches(): Promise<Watch[]> {
  try {
    const rawData = await tryReadWatchData();
    
    if (!rawData || rawData.length === 0) {
      console.error('無法讀取手錶資料');
      return [];
    }

    // 轉換資料 - 遍歷所有行並轉換
    const watches: Watch[] = [];
    for (let i = 1; i < rawData.length; i++) { // 跳過標題行
      try {
        const watch = transformWatchData(rawData[i], i);
        watches.push(watch);
      } catch (error) {
        console.warn(`轉換第 ${i} 行資料失敗:`, error);
      }
    }

    return watches;
  } catch (error) {
    console.error('獲取手錶列表失敗:', error);
    return [];
  }
}
