/**
 * Edge Runtime 兼容的加密工具
 * 使用 Web Crypto API 替代 Node.js crypto 模組
 */

/**
 * 計算 SHA-256 雜湊值
 */
export async function sha256(data: string): Promise<string> {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = new Uint8Array(hashBuffer);
  
  return Array.from(hashArray)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * 計算 HMAC-SHA256
 */
export async function hmacSha256(key: string, data: string): Promise<string> {
  const encoder = new TextEncoder();
  const keyBuffer = encoder.encode(key);
  const dataBuffer = encoder.encode(data);
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HM<PERSON>', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
  const signatureArray = new Uint8Array(signature);
  
  return Array.from(signatureArray)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * 生成隨機字串
 */
export function randomBytes(length: number): string {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  
  return Array.from(array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Base64 編碼
 */
export function base64Encode(data: string): string {
  return btoa(data);
}

/**
 * Base64 解碼
 */
export function base64Decode(data: string): string {
  return atob(data);
}

/**
 * URL 安全的 Base64 編碼
 */
export function base64UrlEncode(data: string): string {
  return btoa(data)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * URL 安全的 Base64 解碼
 */
export function base64UrlDecode(data: string): string {
  // 補齊 padding
  const padded = data + '='.repeat((4 - data.length % 4) % 4);
  const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
  return atob(base64);
}

/**
 * 時間安全的字串比較
 */
export async function timingSafeEqual(a: string, b: string): Promise<boolean> {
  if (a.length !== b.length) {
    return false;
  }
  
  // 使用 HMAC 進行時間安全比較
  const key = randomBytes(32);
  const hmacA = await hmacSha256(key, a);
  const hmacB = await hmacSha256(key, b);
  
  return hmacA === hmacB;
}
