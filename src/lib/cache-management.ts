/**
 * 快取管理系統
 * 提供手動清除、監控和管理功能
 */

import { getCacheStats, clearCache } from './cache-implementation';

// 快取類型定義
export type CacheType = 'blog' | 'watch' | 'faq' | 'all';
export type CacheAction = 'clear' | 'warmup' | 'stats' | 'health';

// 快取管理介面
export interface CacheManager {
  // 清除操作
  clearAll(): Promise<ClearResult>;
  clearByType(type: CacheType): Promise<ClearResult>;
  clearByItem(type: CacheType, id: string): Promise<ClearResult>;
  clearExpired(): Promise<ClearResult>;
  
  // 預熱操作
  warmupAll(): Promise<WarmupResult>;
  warmupByType(type: CacheType): Promise<WarmupResult>;
  
  // 監控操作
  getStats(): Promise<CacheStats>;
  getHealth(): Promise<HealthStatus>;
  
  // 管理操作
  setTTL(type: CacheType, ttl: number): Promise<void>;
  enableLogging(enabled: boolean): Promise<void>;
}

// 結果類型定義
export interface ClearResult {
  success: boolean;
  clearedCount: number;
  type?: CacheType;
  id?: string;
  timestamp: Date;
  duration: number;
}

export interface WarmupResult {
  success: boolean;
  warmedCount: number;
  type?: CacheType;
  timestamp: Date;
  duration: number;
  errors: string[];
}

export interface CacheStats {
  totalItems: number;
  activeItems: number;
  expiredItems: number;
  hitRate: number;
  missRate: number;
  totalSizeBytes: number;
  averageSizeBytes: number;
  oldestItem: Date | null;
  newestItem: Date | null;
  byType: Record<CacheType, TypeStats>;
}

export interface TypeStats {
  count: number;
  sizeBytes: number;
  hitRate: number;
  averageAge: number;
}

export interface HealthStatus {
  healthy: boolean;
  score: number; // 0-100
  issues: HealthIssue[];
  recommendations: string[];
  lastCheck: Date;
}

export interface HealthIssue {
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'performance' | 'memory' | 'consistency' | 'availability';
  message: string;
  metric?: string;
  value?: number;
  threshold?: number;
}

// 快取管理器實作
class CacheManagerImpl implements CacheManager {
  private hitCounts = new Map<string, number>();
  private missCounts = new Map<string, number>();
  private lastHealthCheck: Date | null = null;
  private healthCache: HealthStatus | null = null;

  /**
   * 清除所有快取
   */
  async clearAll(): Promise<ClearResult> {
    const startTime = performance.now();
    
    try {
      clearCache(); // 清除伺服器端快取
      
      // 如果有 CDN，也清除 CDN 快取
      await this.clearCDNCache('all');
      
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        clearedCount: -1, // 全部清除，無法計算確切數量
        type: 'all',
        timestamp: new Date(),
        duration
      };
    } catch (error) {
      console.error('清除所有快取失敗:', error);
      return {
        success: false,
        clearedCount: 0,
        type: 'all',
        timestamp: new Date(),
        duration: performance.now() - startTime
      };
    }
  }

  /**
   * 按類型清除快取
   */
  async clearByType(type: CacheType): Promise<ClearResult> {
    const startTime = performance.now();
    
    try {
      let clearedCount = 0;
      
      // 清除伺服器端快取
      const patterns = this.getTypePatternsForClear(type);
      for (const pattern of patterns) {
        clearCache(pattern);
        clearedCount++;
      }
      
      // 清除 CDN 快取
      await this.clearCDNCache(type);
      
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        clearedCount,
        type,
        timestamp: new Date(),
        duration
      };
    } catch (error) {
      console.error(`清除 ${type} 快取失敗:`, error);
      return {
        success: false,
        clearedCount: 0,
        type,
        timestamp: new Date(),
        duration: performance.now() - startTime
      };
    }
  }

  /**
   * 清除特定項目快取
   */
  async clearByItem(type: CacheType, id: string): Promise<ClearResult> {
    const startTime = performance.now();
    
    try {
      const cacheKey = this.generateCacheKey(type, id);
      clearCache(cacheKey);
      
      // 清除 CDN 中的特定項目
      await this.clearCDNItem(type, id);
      
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        clearedCount: 1,
        type,
        id,
        timestamp: new Date(),
        duration
      };
    } catch (error) {
      console.error(`清除 ${type}:${id} 快取失敗:`, error);
      return {
        success: false,
        clearedCount: 0,
        type,
        id,
        timestamp: new Date(),
        duration: performance.now() - startTime
      };
    }
  }

  /**
   * 清除過期快取
   */
  async clearExpired(): Promise<ClearResult> {
    const startTime = performance.now();
    
    try {
      // 這個功能在 cache-implementation.ts 中已經實作
      const clearedCount = (await import('./cache-implementation')).default.cleanExpired();
      
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        clearedCount,
        timestamp: new Date(),
        duration
      };
    } catch (error) {
      console.error('清除過期快取失敗:', error);
      return {
        success: false,
        clearedCount: 0,
        timestamp: new Date(),
        duration: performance.now() - startTime
      };
    }
  }

  /**
   * 預熱所有快取
   */
  async warmupAll(): Promise<WarmupResult> {
    const startTime = performance.now();
    const errors: string[] = [];
    let warmedCount = 0;
    
    try {
      // 部落格現在使用 MDX 檔案系統，不需要快取預熱
      try {
        console.log('部落格使用 MDX 檔案系統，無需預熱');
        warmedCount++;
      } catch (error) {
        errors.push(`部落格列表預熱失敗: ${error}`);
      }
      
      // 預熱手錶快取
      try {
        const { getCachedWatchListData } = await import('./cache-implementation');
        await getCachedWatchListData();
        warmedCount++;
      } catch (error) {
        errors.push(`手錶列表預熱失敗: ${error}`);
      }
      
      const duration = performance.now() - startTime;
      
      return {
        success: errors.length === 0,
        warmedCount,
        timestamp: new Date(),
        duration,
        errors
      };
    } catch (error) {
      return {
        success: false,
        warmedCount,
        timestamp: new Date(),
        duration: performance.now() - startTime,
        errors: [...errors, `預熱失敗: ${error}`]
      };
    }
  }

  /**
   * 按類型預熱快取
   */
  async warmupByType(type: CacheType): Promise<WarmupResult> {
    const startTime = performance.now();
    const errors: string[] = [];
    let warmedCount = 0;
    
    try {
      switch (type) {
        case 'blog':
          // 部落格現在使用 MDX 檔案系統，不需要快取預熱
          console.log('部落格使用 MDX 檔案系統，無需預熱');
          warmedCount = 1;
          break;
          
        case 'watch':
          const { getCachedWatchListData } = await import('./cache-implementation');
          await getCachedWatchListData();
          warmedCount = 1;
          break;
          
        case 'all':
          return this.warmupAll();
          
        default:
          throw new Error(`不支援的快取類型: ${type}`);
      }
      
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        warmedCount,
        type,
        timestamp: new Date(),
        duration,
        errors
      };
    } catch (error) {
      return {
        success: false,
        warmedCount,
        type,
        timestamp: new Date(),
        duration: performance.now() - startTime,
        errors: [`${type} 預熱失敗: ${error}`]
      };
    }
  }

  /**
   * 獲取快取統計
   */
  async getStats(): Promise<CacheStats> {
    const baseStats = getCacheStats();
    
    // 計算命中率
    const totalHits = Array.from(this.hitCounts.values()).reduce((sum, count) => sum + count, 0);
    const totalMisses = Array.from(this.missCounts.values()).reduce((sum, count) => sum + count, 0);
    const totalRequests = totalHits + totalMisses;
    
    const hitRate = totalRequests > 0 ? totalHits / totalRequests : 0;
    const missRate = totalRequests > 0 ? totalMisses / totalRequests : 0;
    
    return {
      totalItems: baseStats.combined.totalItems,
      activeItems: baseStats.combined.activeItems,
      expiredItems: baseStats.combined.expiredItems,
      totalSizeBytes: baseStats.combined.totalSizeBytes,
      averageSizeBytes: baseStats.combined.totalItems > 0 ? baseStats.combined.totalSizeBytes / baseStats.combined.totalItems : 0,
      hitRate,
      missRate,
      oldestItem: null, // 需要從實際快取中計算
      newestItem: null, // 需要從實際快取中計算
      byType: {
        blog: { count: baseStats.blog.totalItems, sizeBytes: baseStats.blog.totalSizeBytes, hitRate: 0, averageAge: 0 },
        watch: { count: baseStats.watch.totalItems, sizeBytes: baseStats.watch.totalSizeBytes, hitRate: 0, averageAge: 0 },
        faq: { count: baseStats.faq.totalItems, sizeBytes: baseStats.faq.totalSizeBytes, hitRate: 0, averageAge: 0 },
        all: { count: baseStats.combined.totalItems, sizeBytes: baseStats.combined.totalSizeBytes, hitRate, averageAge: 0 }
      }
    };
  }

  /**
   * 獲取健康狀態
   */
  async getHealth(): Promise<HealthStatus> {
    const now = new Date();
    
    // 如果最近檢查過且結果仍有效，返回快取結果
    if (this.lastHealthCheck && this.healthCache && 
        (now.getTime() - this.lastHealthCheck.getTime()) < 60000) { // 1分鐘內
      return this.healthCache;
    }
    
    const stats = await this.getStats();
    const issues: HealthIssue[] = [];
    let score = 100;
    
    // 檢查命中率
    if (stats.hitRate < 0.5) {
      issues.push({
        severity: 'high',
        type: 'performance',
        message: '快取命中率過低',
        metric: 'hitRate',
        value: stats.hitRate,
        threshold: 0.8
      });
      score -= 30;
    } else if (stats.hitRate < 0.8) {
      issues.push({
        severity: 'medium',
        type: 'performance',
        message: '快取命中率偏低',
        metric: 'hitRate',
        value: stats.hitRate,
        threshold: 0.8
      });
      score -= 15;
    }
    
    // 檢查記憶體使用
    const maxSizeBytes = 100 * 1024 * 1024; // 100MB
    if (stats.totalSizeBytes > maxSizeBytes) {
      issues.push({
        severity: 'high',
        type: 'memory',
        message: '快取記憶體使用過高',
        metric: 'totalSizeBytes',
        value: stats.totalSizeBytes,
        threshold: maxSizeBytes
      });
      score -= 25;
    }
    
    // 檢查過期項目比例
    const expiredRatio = stats.totalItems > 0 ? stats.expiredItems / stats.totalItems : 0;
    if (expiredRatio > 0.3) {
      issues.push({
        severity: 'medium',
        type: 'performance',
        message: '過期快取項目過多',
        metric: 'expiredRatio',
        value: expiredRatio,
        threshold: 0.2
      });
      score -= 10;
    }
    
    // 生成建議
    const recommendations: string[] = [];
    if (stats.hitRate < 0.8) {
      recommendations.push('考慮增加快取 TTL 或預熱更多內容');
    }
    if (stats.totalSizeBytes > maxSizeBytes * 0.8) {
      recommendations.push('考慮清理過期快取或減少快取項目');
    }
    if (expiredRatio > 0.2) {
      recommendations.push('建議定期清理過期快取');
    }
    
    const health: HealthStatus = {
      healthy: issues.filter(i => i.severity === 'high' || i.severity === 'critical').length === 0,
      score: Math.max(0, score),
      issues,
      recommendations,
      lastCheck: now
    };
    
    this.lastHealthCheck = now;
    this.healthCache = health;
    
    return health;
  }

  /**
   * 設定 TTL
   */
  async setTTL(type: CacheType, ttl: number): Promise<void> {
    // 這個功能需要在 cache-implementation.ts 中實作動態 TTL 設定
    console.log(`設定 ${type} 的 TTL 為 ${ttl}ms`);
  }

  /**
   * 啟用/停用日誌
   */
  async enableLogging(enabled: boolean): Promise<void> {
    // 這個功能需要在 cache-implementation.ts 中實作
    console.log(`${enabled ? '啟用' : '停用'}快取日誌`);
  }

  // 私有輔助方法
  private getTypePatternsForClear(type: CacheType): string[] {
    switch (type) {
      case 'blog':
        return ['blog-list-data', 'blog-detail-*'];
      case 'watch':
        return ['watch-list-data', 'watch-detail-*'];
      case 'faq':
        return ['faq-*'];
      case 'all':
        return ['*'];
      default:
        return [];
    }
  }

  private generateCacheKey(type: CacheType, id: string): string {
    switch (type) {
      case 'blog':
        return `blog-detail-${id}`;
      case 'watch':
        return `watch-detail-${id}`;
      case 'faq':
        return `faq-${id}`;
      default:
        return `${type}-${id}`;
    }
  }

  private async clearCDNCache(type: CacheType): Promise<void> {
    // 未來實作 Cloudflare CDN 清除
    if (process.env.CLOUDFLARE_API_TOKEN) {
      console.log(`清除 CDN 快取: ${type}`);
      // 實作 Cloudflare purge API 呼叫
    }
  }

  private async clearCDNItem(type: CacheType, id: string): Promise<void> {
    // 未來實作 Cloudflare CDN 特定項目清除
    if (process.env.CLOUDFLARE_API_TOKEN) {
      console.log(`清除 CDN 項目: ${type}:${id}`);
      // 實作 Cloudflare purge API 呼叫
    }
  }

  // 記錄命中/未命中統計
  recordHit(key: string): void {
    this.hitCounts.set(key, (this.hitCounts.get(key) || 0) + 1);
  }

  recordMiss(key: string): void {
    this.missCounts.set(key, (this.missCounts.get(key) || 0) + 1);
  }
}

// 全域快取管理器實例
export const cacheManager = new CacheManagerImpl();

// 便利函數
export async function clearAllCache(): Promise<ClearResult> {
  return cacheManager.clearAll();
}

export async function clearCacheByType(type: CacheType): Promise<ClearResult> {
  return cacheManager.clearByType(type);
}

export async function clearCacheByItem(type: CacheType, id: string): Promise<ClearResult> {
  return cacheManager.clearByItem(type, id);
}

export async function warmupCache(): Promise<WarmupResult> {
  return cacheManager.warmupAll();
}

export async function getCacheHealth(): Promise<HealthStatus> {
  return cacheManager.getHealth();
}

export async function getCacheStatistics(): Promise<CacheStats> {
  return cacheManager.getStats();
}

export default cacheManager;
