// MDX 檔案處理工具
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { MDXBlogPost, MDXBlogListItem, BlogFrontmatter } from '@/types/mdx';

// 部落格內容目錄
const BLOG_CONTENT_DIR = path.join(process.cwd(), 'content', 'blog');

/**
 * 獲取所有 MDX 檔案路徑
 */
export function getAllMdxFiles(): string[] {
  if (!fs.existsSync(BLOG_CONTENT_DIR)) {
    return [];
  }
  
  return fs
    .readdirSync(BLOG_CONTENT_DIR)
    .filter(file => file.endsWith('.mdx') && file !== 'README.md')
    .sort((a, b) => b.localeCompare(a)); // 按檔名降序排列（最新的在前）
}

/**
 * 從檔名提取 slug
 */
export function extractSlugFromFilename(filename: string): string {
  // 移除 .mdx 副檔名
  const nameWithoutExt = filename.replace(/\.mdx$/, '');
  
  // 移除日期前綴 (YYYY-MM-DD-)
  const slug = nameWithoutExt.replace(/^\d{4}-\d{2}-\d{2}-/, '');
  
  return slug;
}

/**
 * 從檔名提取發布日期
 */
export function extractDateFromFilename(filename: string): string {
  const match = filename.match(/^(\d{4}-\d{2}-\d{2})-/);
  return match ? match[1] : new Date().toISOString().split('T')[0];
}

/**
 * 讀取並解析單個 MDX 檔案
 */
export function readMdxFile(filename: string): MDXBlogPost | null {
  try {
    const filePath = path.join(BLOG_CONTENT_DIR, filename);
    
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const { data, content } = matter(fileContent);
    
    // 驗證必要的 frontmatter 欄位
    if (!data.title || !data.slug) {
      console.warn(`MDX 檔案 ${filename} 缺少必要的 frontmatter 欄位`);
      return null;
    }
    
    const frontmatter: BlogFrontmatter = {
      title: data.title,
      publishDate: data.publishDate || extractDateFromFilename(filename),
      author: data.author || 'Weaven',
      thumbnail: data.thumbnail || '',
      seoTitle: data.seoTitle || data.title,
      seoDescription: data.seoDescription || '',
      socialImage: data.socialImage || data.thumbnail || '',
      socialTitle: data.socialTitle || data.title,
      socialDescription: data.socialDescription || data.seoDescription || '',
      slug: data.slug || extractSlugFromFilename(filename),
      tags: data.tags || [],
      excerpt: data.excerpt || '',
      featured: data.featured || false,
    };
    
    return {
      frontmatter,
      content,
      slug: frontmatter.slug || '',
      readTime: calculateReadingTime(content),
    };
  } catch (error) {
    console.error(`讀取 MDX 檔案 ${filename} 失敗:`, error);
    return null;
  }
}

/**
 * 根據 slug 獲取特定文章
 */
export function getMdxPostBySlug(slug: string): MDXBlogPost | null {
  const files = getAllMdxFiles();
  
  for (const filename of files) {
    const post = readMdxFile(filename);
    if (post && post.slug === slug) {
      return post;
    }
  }
  
  return null;
}

/**
 * 獲取所有文章列表（用於列表頁）
 */
export function getAllMdxPosts(): MDXBlogListItem[] {
  const files = getAllMdxFiles();
  const posts: MDXBlogListItem[] = [];
  
  for (const filename of files) {
    const post = readMdxFile(filename);
    if (post) {
      posts.push({
        frontmatter: post.frontmatter,
        slug: post.slug,
        excerpt: post.frontmatter.excerpt || '',
        readTime: post.readTime,
      });
    }
  }
  
  // 按發布日期降序排列
  return posts.sort((a, b) =>
    new Date(b.frontmatter.date || b.frontmatter.publishDate || '').getTime() -
    new Date(a.frontmatter.date || a.frontmatter.publishDate || '').getTime()
  );
}

/**
 * 獲取分頁文章列表
 */
export function getPaginatedMdxPosts(page: number = 0, pageSize: number = 12): {
  posts: MDXBlogListItem[];
  total: number;
  hasMore: boolean;
  page: number;
  pageSize: number;
} {
  const allPosts = getAllMdxPosts();
  const startIndex = page * pageSize;
  const endIndex = startIndex + pageSize;
  
  const posts = allPosts.slice(startIndex, endIndex);
  const hasMore = endIndex < allPosts.length;
  
  return {
    posts,
    total: allPosts.length,
    hasMore,
    page,
    pageSize,
  };
}

/**
 * 獲取精選文章
 */
export function getFeaturedMdxPosts(limit: number = 3): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  return allPosts
    .filter(post => post.frontmatter.featured)
    .slice(0, limit);
}

/**
 * 根據標籤獲取相關文章
 */
export function getRelatedMdxPosts(currentSlug: string, tags: string[], limit: number = 3): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  
  return allPosts
    .filter(post => post.slug !== currentSlug) // 排除當前文章
    .filter(post => {
      // 檢查是否有共同標籤
      const postTags = post.frontmatter.tags || [];
      return tags.some(tag => postTags.includes(tag));
    })
    .slice(0, limit);
}

/**
 * 計算閱讀時間（分鐘）
 */
export function calculateReadingTime(content: string): number {
  // 移除 Markdown 語法和 HTML 標籤
  const plainText = content
    .replace(/```[\s\S]*?```/g, '') // 移除程式碼區塊
    .replace(/`[^`]*`/g, '') // 移除行內程式碼
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除圖片
    .replace(/\[.*?\]\(.*?\)/g, '') // 移除連結
    .replace(/<[^>]*>/g, '') // 移除 HTML 標籤
    .replace(/[#*_~`]/g, '') // 移除 Markdown 語法
    .trim();
  
  // 計算字數（中文字符和英文單字）
  const chineseChars = (plainText.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (plainText.match(/[a-zA-Z]+/g) || []).length;
  
  // 假設中文閱讀速度 300 字/分鐘，英文 200 詞/分鐘
  const readingTimeMinutes = Math.ceil((chineseChars / 300) + (englishWords / 200));
  
  return Math.max(1, readingTimeMinutes); // 最少 1 分鐘
}

/**
 * 獲取所有可用的標籤
 */
export function getAllTags(): string[] {
  const allPosts = getAllMdxPosts();
  const tagSet = new Set<string>();
  
  allPosts.forEach(post => {
    (post.frontmatter.tags || []).forEach(tag => tagSet.add(tag));
  });
  
  return Array.from(tagSet).sort();
}

/**
 * 根據標籤獲取文章
 */
export function getPostsByTag(tag: string): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  
  return allPosts.filter(post => 
    (post.frontmatter.tags || []).includes(tag)
  );
}

/**
 * 搜尋文章
 */
export function searchMdxPosts(query: string): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  const lowercaseQuery = query.toLowerCase();
  
  return allPosts.filter(post => {
    const { title, excerpt, tags } = post.frontmatter;

    return (
      title.toLowerCase().includes(lowercaseQuery) ||
      (excerpt || '').toLowerCase().includes(lowercaseQuery) ||
      (tags || []).some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  });
}

/**
 * 驗證 MDX 檔案格式
 */
export function validateMdxFile(filename: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  try {
    const post = readMdxFile(filename);
    
    if (!post) {
      errors.push('無法讀取檔案');
      return { isValid: false, errors };
    }
    
    const { frontmatter } = post;
    
    // 檢查必要欄位
    if (!frontmatter.title) errors.push('缺少 title');
    if (!frontmatter.slug) errors.push('缺少 slug');
    if (!frontmatter.publishDate) errors.push('缺少 publishDate');
    if (!frontmatter.seoDescription) errors.push('缺少 seoDescription');
    
    // 檢查日期格式
    if (frontmatter.publishDate && isNaN(Date.parse(frontmatter.publishDate))) {
      errors.push('publishDate 格式不正確');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  } catch (error) {
    errors.push(`檔案解析錯誤: ${error instanceof Error ? error.message : String(error)}`);
    return { isValid: false, errors };
  }
}
