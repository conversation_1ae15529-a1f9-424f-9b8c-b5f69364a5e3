// 靜態路由生成工具
import { getAllMdxFiles, extractSlugFromFilename } from './mdx-utils';

/**
 * 生成部落格文章的靜態路徑
 * 用於 Next.js 的 generateStaticParams
 */
export function generateBlogStaticParams(): { slug: string }[] {
  const files = getAllMdxFiles();
  
  return files.map(filename => ({
    slug: extractSlugFromFilename(filename),
  }));
}

/**
 * 生成標籤頁面的靜態路徑
 */
export function generateTagStaticParams(): { tag: string }[] {
  // 這個功能可以在未來實作標籤頁面時使用
  // 目前先返回空陣列
  return [];
}

/**
 * 檢查 slug 是否存在
 */
export function isValidBlogSlug(slug: string): boolean {
  const files = getAllMdxFiles();
  
  return files.some(filename => {
    const fileSlug = extractSlugFromFilename(filename);
    return fileSlug === slug;
  });
}
