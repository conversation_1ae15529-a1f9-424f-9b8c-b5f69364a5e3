/**
 * Edge Runtime 兼容的 PayUni 加密模組
 * 使用 Web Crypto API 替代 Node.js crypto 模組
 */

import { PAYUNI_CONFIG } from '@/config/environment-config';

/**
 * 將物件轉換為查詢字串格式（模擬 Node.js querystring.stringify）
 * 重要：必須與原始 Node.js querystring.stringify 的輸出格式完全一致
 */
function stringifyParams(obj: Record<string, string | number | boolean>): string {
  // 模擬 Node.js querystring.stringify 的行為
  const pairs: string[] = [];
  for (const [key, value] of Object.entries(obj)) {
    // 使用 encodeURIComponent 進行編碼，與 querystring.stringify 一致
    const encodedKey = encodeURIComponent(key);
    const encodedValue = encodeURIComponent(String(value));
    pairs.push(`${encodedKey}=${encodedValue}`);
  }
  return pairs.join('&');
}

/**
 * AES-256-GCM 加密 (PayUni 使用的模式) - Edge Runtime 版本
 * 根據 PayUni 官方文檔實現
 */
async function encryptAESEdge(plaintext: string): Promise<string> {
  const HASH_KEY = PAYUNI_CONFIG.getHashKey();
  const HASH_IV = PAYUNI_CONFIG.getHashIV();

  if (!HASH_KEY || !HASH_IV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  // 準備密鑰和 IV (完全按照 Node.js 原始實現)
  // 重要：Node.js crypto.createCipheriv 直接接受字串，我們需要模擬相同的轉換

  // HASH_KEY: Node.js 直接使用字串，我們需要轉為 UTF-8 bytes
  const keyBuffer = new TextEncoder().encode(HASH_KEY);

  // HASH_IV: Node.js 使用 Buffer.from(HASH_IV)，轉為 UTF-8 bytes
  const ivBuffer = new TextEncoder().encode(HASH_IV);

  // 確保 IV 長度正確 (PayUni 使用 16 bytes IV)
  const finalIvBuffer = new Uint8Array(16); // PayUni 使用 16 bytes IV
  for (let i = 0; i < Math.min(ivBuffer.length, 16); i++) {
    finalIvBuffer[i] = ivBuffer[i];
  }

  // 導入密鑰
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'AES-GCM' },
    false,
    ['encrypt']
  );

  // 加密
  const plaintextBuffer = new TextEncoder().encode(plaintext);
  const encrypted = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv: finalIvBuffer,
    },
    cryptoKey,
    plaintextBuffer
  );

  // 提取密文和認證標籤 (最後 16 bytes 是 AuthTag)
  const encryptedArray = new Uint8Array(encrypted);
  const ciphertext = encryptedArray.slice(0, -16); // 密文部分
  const authTag = encryptedArray.slice(-16); // 認證標籤 (最後 16 bytes)

  // 轉換為 base64 (按照原始 Node.js 實現)
  const ciphertextBase64 = btoa(String.fromCharCode(...ciphertext));
  const authTagBase64 = btoa(String.fromCharCode(...authTag));

  // 組合格式：ciphertext:::tag (按照原始 Node.js 實現)
  const combined = `${ciphertextBase64}:::${authTagBase64}`;

  // 將組合字串轉為 hex (按照原始 Node.js 實現: Buffer.from(combined).toString('hex'))
  const combinedBuffer = new TextEncoder().encode(combined);
  const hexResult = Array.from(combinedBuffer)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
    .trim();

  return hexResult;
}

/**
 * AES-256-GCM 解密 - Edge Runtime 版本
 * 根據 PayUni 官方文檔實現
 */
async function decryptAESEdge(encryptedHex: string, hashKey: string, hashIV: string): Promise<string> {
  try {
    // 將 hex 轉換回 bytes (按照 PayUni 格式)
    const encryptedBytes = new Uint8Array(
      encryptedHex.match(/.{1,2}/g)?.map(byte => parseInt(byte, 16)) || []
    );

    // 轉換為字串並分割 ciphertext:::tag
    const encryptedString = new TextDecoder().decode(encryptedBytes);
    const [ciphertextBase64, authTagBase64] = encryptedString.split(':::');

    if (!ciphertextBase64 || !authTagBase64) {
      throw new Error('Invalid encrypted data format');
    }

    // 解碼 base64
    const ciphertext = Uint8Array.from(atob(ciphertextBase64), c => c.charCodeAt(0));
    const authTag = Uint8Array.from(atob(authTagBase64), c => c.charCodeAt(0));

    // 組合密文和認證標籤 (GCM 格式)
    const combined = new Uint8Array(ciphertext.length + authTag.length);
    combined.set(ciphertext);
    combined.set(authTag, ciphertext.length);

    // 準備密鑰和 IV (完全按照 Node.js 原始實現)
    const keyBuffer = new TextEncoder().encode(hashKey);

    // HASH_IV: Node.js 使用 Buffer.from(hashIV)，轉為 UTF-8 bytes
    const ivBuffer = new TextEncoder().encode(hashIV);

    // 確保 IV 長度正確 (與加密時一致)
    const finalIvBuffer = new Uint8Array(12); // 標準 AES-GCM IV 長度
    for (let i = 0; i < Math.min(ivBuffer.length, 12); i++) {
      finalIvBuffer[i] = ivBuffer[i];
    }

    // 導入密鑰
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );

    // 解密
    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: finalIvBuffer,
      },
      cryptoKey,
      combined
    );

    return new TextDecoder().decode(decrypted);
  } catch (error) {
    console.error('AES 解密失敗:', error);
    throw new Error('解密失敗');
  }
}

/**
 * SHA256 加密 - Edge Runtime 版本
 */
async function sha256Edge(encryptStr: string): Promise<string> {
  const HASH_KEY = PAYUNI_CONFIG.getHashKey();
  const HASH_IV = PAYUNI_CONFIG.getHashIV();

  if (!HASH_KEY || !HASH_IV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const data = `${HASH_KEY}${encryptStr}${HASH_IV}`;
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = new Uint8Array(hashBuffer);
  
  return Array.from(hashArray)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
    .toUpperCase();
}

/**
 * 計算 ATM 轉帳動態到期日期
 */
export function calculateATMExpireDate(): string {
  const now = new Date();
  const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000)); // UTC+8
  
  try {
    const currentHour = taiwanTime.getHours();
    let expireDate: Date;
    
    if (currentHour >= 14) { // 14:00 (2PM) 之後
      expireDate = new Date(taiwanTime.getTime() + (3 * 24 * 60 * 60 * 1000)); // +3 天
    } else { // 14:00 之前或等於
      expireDate = new Date(taiwanTime.getTime() + (2 * 24 * 60 * 60 * 1000)); // +2 天
    }
    
    return expireDate.toISOString().split('T')[0].replace(/-/g, '');
  } catch (error) {
    console.error('計算 ATM 到期日期失敗，使用預設值 +2 天:', error);
    const fallbackDate = new Date(taiwanTime.getTime() + (2 * 24 * 60 * 60 * 1000));
    return fallbackDate.toISOString().split('T')[0].replace(/-/g, '');
  }
}

/**
 * 建立 PayUni 付款請求所需參數 - Edge Runtime 版本
 */
export async function createPaymentRequestEdge(tradeData: Record<string, string | number | boolean>) {
  const merID = PAYUNI_CONFIG.getMerchantId();

  if (!merID) {
    throw new Error(`PayUni 商店 ID 未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const encryptInfoString = stringifyParams(tradeData);
  const encryptInfo = await encryptAESEdge(encryptInfoString);
  const hashInfo = await sha256Edge(encryptInfo);

  return {
    MerID: merID,
    EncryptInfo: encryptInfo,
    HashInfo: hashInfo,
  };
}

/**
 * 解密 PayUni 資料 - Edge Runtime 版本
 */
export async function decryptPayUniDataEdge(encryptInfo: string, hashKey: string, hashIV: string): Promise<Record<string, string>> {
  const decryptedString = await decryptAESEdge(encryptInfo, hashKey, hashIV);
  
  // 解析 URL 編碼的字串
  const params = new URLSearchParams(decryptedString);
  const result: Record<string, string> = {};
  
  for (const [key, value] of params.entries()) {
    result[key] = value;
  }
  
  return result;
}

/**
 * 解析 PayUni Webhook 回應 - Edge Runtime 版本
 */
export async function parseWebhookResponseEdge(encryptInfo: string): Promise<Record<string, string>> {
  const hashKey = PAYUNI_CONFIG.getHashKey();
  const hashIV = PAYUNI_CONFIG.getHashIV();

  if (!hashKey || !hashIV) {
    throw new Error(`PayUni 憑證未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  return await decryptPayUniDataEdge(encryptInfo, hashKey, hashIV);
}

/**
 * 查詢 PayUni 訂單狀態 - Edge Runtime 版本
 */
export async function queryPayUniOrderEdge(orderNo: string): Promise<Record<string, string>> {
  const merID = PAYUNI_CONFIG.getMerchantId();
  const apiUrl = PAYUNI_CONFIG.getApiUrl();

  if (!merID) {
    throw new Error(`PayUni 商店 ID 未設定 (環境: ${PAYUNI_CONFIG.ENVIRONMENT})`);
  }

  const tradeData = {
    MerID: merID,
    MerTradeNo: orderNo,
    Timestamp: Math.floor(Date.now() / 1000),
  };

  const { EncryptInfo, HashInfo } = await createPaymentRequestEdge(tradeData);

  const response = await fetch(`${apiUrl}/api/trade/query`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: stringifyParams({
      MerID: merID,
      EncryptInfo,
      HashInfo,
      Version: '1.0',
    }),
  });

  if (!response.ok) {
    throw new Error(`PayUni API 請求失敗: ${response.status}`);
  }

  const responseText = await response.text();
  
  try {
    const responseData = JSON.parse(responseText);
    
    if (responseData.Status === 'SUCCESS' && responseData.EncryptInfo) {
      return await decryptPayUniDataEdge(responseData.EncryptInfo, PAYUNI_CONFIG.getHashKey(), PAYUNI_CONFIG.getHashIV());
    } else {
      throw new Error(`PayUni 查詢失敗: ${responseData.Message || '未知錯誤'}`);
    }
  } catch (error) {
    console.error('解析 PayUni 回應失敗:', error);
    throw new Error('解析付款查詢回應失敗');
  }
}

/**
 * 轉換 PayUni 訂單狀態碼為中文
 */
export function convertTradeStatus(tradeStatus: string | number): string {
  const status = String(tradeStatus);
  switch (status) {
    case '0': return '取號成功';
    case '1': return '已付款';
    case '2': return '付款失敗';
    case '3': return '已取消';
    case '4': return '已退款';
    default: return '未知狀態';
  }
}

/**
 * 轉換 PayUni 支付方式代碼為中文
 */
export function convertPaymentType(paymentType: string | number): string {
  const type = String(paymentType);
  switch (type) {
    case '1': return '信用卡';
    case '2': return 'ATM轉帳';
    case '3': return '條碼/代碼';
    case '4': return '儲值/點數';
    case '5': return '貨到付款';
    case '6': return '取貨付款';
    case '7': return '信用卡分期';
    default: return '其他';
  }
}

/**
 * 綜合判斷 PayUni 訂單的整體付款狀態
 */
export function getOverallPaymentStatus(payuniData: Record<string, unknown>): string {
  const tradeStatus = String(payuniData.TradeStatus || '');
  const refundStatus = String(payuniData.RefundStatus || '');
  const refundAmt = parseInt(String(payuniData.RefundAmt || '0'));

  // 優先檢查退款狀態 - 統一顯示為"已退款"
  if (refundStatus === '2' || refundAmt > 0) {
    return '已退款';
  }

  // 根據交易狀態判斷
  switch (tradeStatus) {
    case '0': return 'ATM取號成功';
    case '1': return '付款成功';
    case '2': return '付款失敗';
    case '3': return '已取消';
    case '4': return '已退款';
    default: return '處理中';
  }
}

// 兼容性函數，保持與原有 API 相同
export const createPaymentRequest = createPaymentRequestEdge;
export const decryptPayUniData = decryptPayUniDataEdge;
export const parseWebhookResponse = parseWebhookResponseEdge;
export const queryPayUniOrder = queryPayUniOrderEdge;
