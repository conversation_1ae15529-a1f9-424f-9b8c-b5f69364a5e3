/**
 * Edge Runtime 兼容的 MDX 工具
 * 將 MDX 內容內嵌到程式碼中，避免使用 fs 模組
 */

import { MDXBlogPost, MDXBlogListItem, BlogFrontmatter } from '@/types/mdx';

// 內嵌的 MDX 部落格文章資料
const EMBEDDED_BLOG_POSTS: Array<{
  slug: string;
  frontmatter: BlogFrontmatter;
  content: string;
}> = [
  {
    slug: 'in-house-movement-and-third-party-movement-guide',
    frontmatter: {
      title: '自製機芯 vs 第三方機芯：手錶愛好者完整指南',
      description: '深入了解自製機芯與第三方機芯的差異，包括優缺點分析、知名品牌案例，以及如何選擇適合的手錶機芯類型。',
      date: '2025-04-29',
      author: 'PANGEA 編輯團隊',
      tags: ['機芯', '手錶知識', '購買指南'],
      category: 'knowledge',
      featured: true,
      readTime: 8,
      seoTitle: '自製機芯 vs 第三方機芯完整比較指南 | PANGEA',
      seoDescription: '專業解析自製機芯與第三方機芯差異，包含 ETA、Sellita、Miyota 等知名機芯品牌比較，助您選擇理想手錶。',
      seoKeywords: ['自製機芯', '第三方機芯', 'ETA機芯', 'Sellita', 'Miyota', '手錶機芯', '機芯比較']
    },
    content: `# 自製機芯 vs 第三方機芯：手錶愛好者完整指南

在手錶世界中，機芯是手錶的心臟，決定了手錶的精準度、耐用性和價值。對於手錶愛好者來說，了解自製機芯（In-house Movement）與第三方機芯（Third-party Movement）的差異是非常重要的。

## 什麼是自製機芯？

自製機芯是指手錶品牌自行研發、設計和製造的機芯。這些機芯通常具有品牌獨特的技術特色和設計理念。

### 自製機芯的優點

1. **獨特性**：每個品牌的自製機芯都有其獨特的設計和技術特色
2. **品質控制**：品牌可以完全控制機芯的品質和規格
3. **技術創新**：能夠根據品牌需求進行技術創新和改進
4. **收藏價值**：通常具有較高的收藏價值和投資潛力

### 自製機芯的缺點

1. **成本較高**：研發和製造成本高，反映在手錶售價上
2. **維修複雜**：需要專業技師和特殊零件進行維修
3. **可靠性風險**：新開發的機芯可能存在未知的可靠性問題

## 什麼是第三方機芯？

第三方機芯是指由專業機芯製造商生產，供多個手錶品牌使用的標準化機芯。

### 知名第三方機芯製造商

1. **ETA（瑞士）**：最知名的機芯供應商，產品線完整
2. **Sellita（瑞士）**：ETA 的主要競爭對手，品質優良
3. **Miyota（日本）**：Citizen 旗下，性價比優秀
4. **Seiko（日本）**：主要供應自家品牌，偶爾對外供應

### 第三方機芯的優點

1. **成熟可靠**：經過大量生產和使用驗證，可靠性高
2. **維修便利**：零件容易取得，維修技師熟悉
3. **成本效益**：大量生產降低成本，手錶售價相對親民
4. **標準化**：規格統一，便於製錶商使用

### 第三方機芯的缺點

1. **缺乏獨特性**：多個品牌使用相同機芯，缺乏差異化
2. **供應風險**：依賴外部供應商，可能面臨供應中斷
3. **技術限制**：無法完全客製化，受限於既有規格

## 如何選擇？

### 選擇自製機芯的情況

- 追求獨特性和品牌特色
- 重視收藏價值和投資潛力
- 預算充足，不介意較高的價格
- 喜歡技術創新和複雜功能

### 選擇第三方機芯的情況

- 重視性價比和實用性
- 希望維修保養更便利
- 預算有限，追求高品質
- 重視機芯的成熟度和可靠性

## 結論

自製機芯和第三方機芯各有優缺點，選擇哪種主要取決於個人需求、預算和偏好。無論選擇哪種，最重要的是選擇信譽良好的品牌和可靠的機芯。

在 PANGEA，我們提供各種搭載不同機芯的優質手錶，無論您偏好自製機芯的獨特性還是第三方機芯的可靠性，都能找到適合的選擇。`
  },
  {
    slug: '5-things-about-watch-water-resistance',
    frontmatter: {
      title: '手錶防水等級完整解析：5個你必須知道的重點',
      description: '詳細解析手錶防水等級標示、實際防水能力，以及日常使用注意事項，讓你正確理解和使用防水手錶。',
      date: '2025-03-07',
      author: 'PANGEA 編輯團隊',
      tags: ['防水', '手錶保養', '使用指南'],
      category: 'maintenance',
      featured: true,
      readTime: 6,
      seoTitle: '手錶防水等級完整指南：ATM、BAR、米數標示解析 | PANGEA',
      seoDescription: '專業解析手錶防水等級標示含義，包含 30M、50M、100M、200M 防水能力說明，以及正確的防水手錶使用方法。',
      seoKeywords: ['手錶防水', '防水等級', 'ATM', 'BAR', '防水手錶', '手錶保養', '防水標示']
    },
    content: `# 手錶防水等級完整解析：5個你必須知道的重點

手錶的防水功能是現代手錶的重要特色之一，但許多人對防水等級的標示和實際能力存在誤解。本文將詳細解析手錶防水的各個面向。

## 1. 防水等級標示解析

### 常見的防水標示方式

- **米數標示**：30M、50M、100M、200M 等
- **ATM 標示**：3ATM、5ATM、10ATM、20ATM 等
- **BAR 標示**：3BAR、5BAR、10BAR、20BAR 等

### 標示對照表

| 標示 | 等效標示 | 防水能力 | 適用場景 |
|------|----------|----------|----------|
| 30M | 3ATM/3BAR | 日常防水 | 洗手、雨水 |
| 50M | 5ATM/5BAR | 生活防水 | 洗臉、淋浴 |
| 100M | 10ATM/10BAR | 游泳防水 | 游泳、水上運動 |
| 200M | 20ATM/20BAR | 潛水防水 | 潛水、專業水上活動 |

## 2. 防水等級的實際意義

### 30M/3ATM 防水
- **不適合**：游泳、潛水、淋浴
- **適合**：日常洗手、遇到雨水
- **注意**：僅能抵抗輕微的水花和濕氣

### 50M/5ATM 防水
- **不適合**：游泳、潛水
- **適合**：洗手、洗臉、短時間淋浴
- **注意**：避免長時間接觸熱水

### 100M/10ATM 防水
- **適合**：游泳、水上運動
- **不適合**：潛水、高壓水柱
- **注意**：可以游泳但避免跳水

### 200M/20ATM 以上防水
- **適合**：潛水、所有水上活動
- **專業級**：通常具備潛水錶認證
- **注意**：定期檢查防水性能

## 3. 影響防水性能的因素

### 溫度變化
- 熱水會使橡膠密封圈膨脹
- 冷水會使密封圈收縮
- 溫差過大會影響密封效果

### 使用年限
- 密封圈會隨時間老化
- 建議每 2-3 年檢查防水性能
- 更換電池時一併檢查密封圈

### 外力衝擊
- 撞擊可能損壞錶殼密封
- 按壓按鈕時要注意防水
- 避免在水中操作錶冠和按鈕

## 4. 正確的防水手錶使用方法

### 日常使用注意事項
1. **避免熱水**：不要在熱水浴或桑拿時佩戴
2. **檢查錶冠**：確保錶冠完全鎖緊
3. **定期保養**：按時進行防水性能檢測
4. **避免化學品**：遠離肥皂、洗髮精等化學物質

### 游泳和潛水使用
1. **確認等級**：確保手錶達到相應防水等級
2. **檢查密封**：下水前檢查所有密封部位
3. **沖洗清潔**：海水或泳池水後要用清水沖洗
4. **自然晾乾**：避免用熱風吹乾

## 5. 防水手錶的保養維護

### 日常保養
- 用清水輕輕沖洗錶殼和錶帶
- 用軟布擦乾，避免水分殘留
- 定期檢查錶帶和錶殼的密封狀況

### 專業保養
- 每 2-3 年進行專業防水測試
- 更換老化的密封圈
- 檢查錶殼是否有裂痕或變形

### 保養時機
- 更換電池時
- 手錶受到撞擊後
- 發現錶內有水氣時
- 定期保養時

## 結論

正確理解手錶防水等級和使用方法，能夠有效保護您的手錶，延長使用壽命。記住，防水不等於防蒸氣，即使是高防水等級的手錶，也要避免極端溫度和化學物質的接觸。

在 PANGEA，我們提供各種防水等級的優質手錶，並提供專業的防水性能檢測和保養服務，確保您的手錶始終保持最佳狀態。`
  }
];

// 記憶體快取
let postsCache: MDXBlogPost[] | null = null;

/**
 * 獲取所有 MDX 部落格文章
 */
export function getAllMdxPosts(): MDXBlogPost[] {
  if (!postsCache) {
    postsCache = EMBEDDED_BLOG_POSTS.map(post => ({
      slug: post.slug,
      frontmatter: post.frontmatter,
      content: post.content,
      excerpt: generateExcerpt(post.content),
      readTime: post.frontmatter.readTime || calculateReadTime(post.content),
      wordCount: countWords(post.content)
    })).sort((a, b) => new Date(b.frontmatter.date || '').getTime() - new Date(a.frontmatter.date || '').getTime());
  }
  return postsCache;
}

/**
 * 獲取分頁的 MDX 部落格文章
 */
export function getPaginatedMdxPosts(page: number = 0, pageSize: number = 12): {
  posts: MDXBlogListItem[];
  total: number;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
} {
  const allPosts = getAllMdxPosts();
  const startIndex = page * pageSize;
  const endIndex = startIndex + pageSize;
  
  const paginatedPosts = allPosts.slice(startIndex, endIndex);
  const totalPages = Math.ceil(allPosts.length / pageSize);
  
  const listItems: MDXBlogListItem[] = paginatedPosts.map(post => ({
    slug: post.slug,
    frontmatter: post.frontmatter,
    excerpt: post.excerpt || '',
    readTime: post.readTime,
    wordCount: post.wordCount
  }));

  return {
    posts: listItems,
    total: allPosts.length,
    hasMore: endIndex < allPosts.length,
    currentPage: page,
    totalPages
  };
}

/**
 * 根據 slug 獲取單一 MDX 部落格文章
 */
export function getMdxPostBySlug(slug: string): MDXBlogPost | null {
  const allPosts = getAllMdxPosts();
  return allPosts.find(post => post.slug === slug) || null;
}

/**
 * 獲取特色文章
 */
export function getFeaturedMdxPosts(): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  return allPosts
    .filter(post => post.frontmatter.featured)
    .slice(0, 3)
    .map(post => ({
      slug: post.slug,
      frontmatter: post.frontmatter,
      excerpt: post.excerpt || '',
      readTime: post.readTime,
      wordCount: post.wordCount
    }));
}

/**
 * 根據標籤搜尋文章
 */
export function getMdxPostsByTag(tag: string): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  return allPosts
    .filter(post => post.frontmatter.tags?.includes(tag))
    .map(post => ({
      slug: post.slug,
      frontmatter: post.frontmatter,
      excerpt: post.excerpt || '',
      readTime: post.readTime,
      wordCount: post.wordCount
    }));
}

/**
 * 根據分類搜尋文章
 */
export function getMdxPostsByCategory(category: string): MDXBlogListItem[] {
  const allPosts = getAllMdxPosts();
  return allPosts
    .filter(post => post.frontmatter.category === category)
    .map(post => ({
      slug: post.slug,
      frontmatter: post.frontmatter,
      excerpt: post.excerpt || '',
      readTime: post.readTime,
      wordCount: post.wordCount
    }));
}

/**
 * 獲取所有標籤
 */
export function getAllMdxTags(): string[] {
  const allPosts = getAllMdxPosts();
  const tagSet = new Set<string>();
  
  allPosts.forEach(post => {
    post.frontmatter.tags?.forEach(tag => tagSet.add(tag));
  });
  
  return Array.from(tagSet).sort();
}

/**
 * 獲取所有分類
 */
export function getAllMdxCategories(): string[] {
  const allPosts = getAllMdxPosts();
  const categorySet = new Set<string>();
  
  allPosts.forEach(post => {
    if (post.frontmatter.category) {
      categorySet.add(post.frontmatter.category);
    }
  });
  
  return Array.from(categorySet).sort();
}

/**
 * 生成文章摘要
 */
function generateExcerpt(content: string, maxLength: number = 200): string {
  // 移除 Markdown 語法
  const plainText = content
    .replace(/^#.*$/gm, '') // 移除標題
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗體
    .replace(/\*(.*?)\*/g, '$1') // 移除斜體
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除連結
    .replace(/```[\s\S]*?```/g, '') // 移除程式碼區塊
    .replace(/`(.*?)`/g, '$1') // 移除行內程式碼
    .replace(/\n+/g, ' ') // 將換行替換為空格
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  return plainText.substring(0, maxLength).trim() + '...';
}

/**
 * 計算閱讀時間（分鐘）
 */
function calculateReadTime(content: string): number {
  const wordsPerMinute = 200; // 平均閱讀速度
  const wordCount = countWords(content);
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * 計算字數
 */
function countWords(content: string): number {
  // 移除 Markdown 語法後計算字數
  const plainText = content
    .replace(/^#.*$/gm, '')
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/\[(.*?)\]\(.*?\)/g, '$1')
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`(.*?)`/g, '$1')
    .trim();

  // 中文字符和英文單詞分別計算
  const chineseChars = (plainText.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (plainText.match(/[a-zA-Z]+/g) || []).length;
  
  return chineseChars + englishWords;
}

/**
 * 清除快取（用於測試）
 */
export function clearMdxCache(): void {
  postsCache = null;
}
