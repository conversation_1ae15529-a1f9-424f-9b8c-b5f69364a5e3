/**
 * Edge Runtime 兼容的 FAQ 模組
 * 將 FAQ 資料內嵌到程式碼中，避免使用 fs 模組
 */

import { FAQ, FAQListItem } from '@/types/faq';

// 內嵌的 FAQ 資料
const FAQ_DATA: FAQ[] = [
  {
    id: 'pangea-intro',
    question: '什麼是 PANGEA？',
    answer: 'PANGEA 是一個專注於高品質手錶的品牌，致力於為客戶提供精緻的手錶產品和專業的服務。我們結合傳統工藝與現代設計，打造獨特的手錶體驗。',
    tags: ['品牌介紹', '關於我們'],
    category: 'general',
    priority: 1,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'watch-warranty',
    question: '手錶保固期多長？',
    answer: '我們的手錶提供 2 年國際保固，保固期內如有非人為損壞的品質問題，我們將提供免費維修或更換服務。保固範圍包括機芯、錶殼、錶帶等主要部件。',
    tags: ['保固', '售後服務', '維修'],
    category: 'warranty',
    priority: 2,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'shipping-info',
    question: '運送方式和時間？',
    answer: '我們提供多種運送方式：\n\n1. 標準宅配：3-5 個工作天\n2. 快速宅配：1-2 個工作天\n3. 超商取貨：3-7 個工作天\n4. 門市自取：當日可取\n\n所有商品均有完整包裝和保險。',
    tags: ['運送', '物流', '取貨'],
    category: 'shipping',
    priority: 3,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'payment-methods',
    question: '支援哪些付款方式？',
    answer: '我們支援以下付款方式：\n\n1. 信用卡（Visa、MasterCard、JCB）\n2. ATM 轉帳\n3. 超商代碼繳費\n4. 分期付款（3、6、12 期）\n5. 門市現金付款\n\n所有線上付款均採用 SSL 加密保護。',
    tags: ['付款', '信用卡', 'ATM', '分期'],
    category: 'payment',
    priority: 4,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'return-policy',
    question: '退換貨政策？',
    answer: '購買後 7 天內可申請退換貨，商品需保持全新狀態且包裝完整。退換貨流程：\n\n1. 聯繫客服申請\n2. 填寫退換貨單\n3. 寄回商品\n4. 檢查確認後退款\n\n客製化商品恕不接受退換貨。',
    tags: ['退貨', '換貨', '退款'],
    category: 'return',
    priority: 5,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'watch-maintenance',
    question: '手錶如何保養？',
    answer: '手錶保養建議：\n\n1. 避免接觸強磁場\n2. 定期清潔錶帶和錶殼\n3. 避免劇烈撞擊\n4. 防水錶款也要避免熱水\n5. 建議每 2-3 年進行專業保養\n\n我們提供專業的保養服務。',
    tags: ['保養', '維護', '清潔'],
    category: 'maintenance',
    priority: 6,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'size-guide',
    question: '如何選擇合適的錶帶尺寸？',
    answer: '錶帶尺寸選擇指南：\n\n1. 測量手腕周長\n2. 加上 1-2cm 的舒適空間\n3. 參考我們的尺寸對照表\n4. 可調式錶帶適用範圍更廣\n\n如需協助，歡迎到門市試戴或聯繫客服。',
    tags: ['尺寸', '錶帶', '試戴'],
    category: 'sizing',
    priority: 7,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'water-resistance',
    question: '防水等級說明？',
    answer: '防水等級說明：\n\n• 30M/3ATM：日常防水，可抵抗雨水和洗手\n• 50M/5ATM：可游泳，但不建議潛水\n• 100M/10ATM：可進行水上運動\n• 200M/20ATM：可進行潛水活動\n\n注意：熱水會影響防水性能。',
    tags: ['防水', '游泳', '潛水'],
    category: 'features',
    priority: 8,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'battery-life',
    question: '石英錶電池壽命？',
    answer: '石英錶電池壽命通常為 2-3 年，實際使用時間會因以下因素而異：\n\n1. 錶款功能複雜度\n2. 使用頻率\n3. 環境溫度\n4. 電池品質\n\n當手錶開始走時不準或停止時，建議更換電池。',
    tags: ['電池', '石英錶', '更換'],
    category: 'maintenance',
    priority: 9,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'contact-info',
    question: '如何聯繫客服？',
    answer: '聯繫客服方式：\n\n• 客服電話：02-1234-5678\n• 服務時間：週一至週五 9:00-18:00\n• 電子郵件：<EMAIL>\n• 線上客服：官網右下角聊天視窗\n• 門市地址：台北市信義區松仁路 XX 號\n\n我們將盡快為您服務。',
    tags: ['客服', '聯繫', '門市'],
    category: 'contact',
    priority: 10,
    lastUpdated: '2024-01-15'
  }
];

// 記憶體快取
let faqCache: FAQ[] | null = null;
let tagCache: string[] | null = null;

/**
 * 獲取所有 FAQ 資料
 */
export function getAllFAQs(): FAQ[] {
  if (!faqCache) {
    faqCache = FAQ_DATA.sort((a, b) => (a.priority || 0) - (b.priority || 0));
  }
  return faqCache;
}

/**
 * 獲取 FAQ 列表項目（不包含完整答案）
 */
export function getFAQListItems(): FAQListItem[] {
  const faqs = getAllFAQs();
  return faqs.map(faq => ({
    id: faq.id,
    question: faq.question,
    tags: faq.tags,
    category: faq.category,
    priority: faq.priority,
    lastUpdated: faq.lastUpdated
  }));
}

/**
 * 根據 ID 獲取特定 FAQ
 */
export function getFAQById(id: string): FAQ | null {
  const faqs = getAllFAQs();
  return faqs.find(faq => faq.id === id) || null;
}

/**
 * 搜尋 FAQ
 */
export function searchFAQs(query: string): FAQ[] {
  if (!query.trim()) {
    return getAllFAQs();
  }

  const faqs = getAllFAQs();
  const searchTerm = query.toLowerCase();

  return faqs.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm) ||
    faq.answer.toLowerCase().includes(searchTerm) ||
    faq.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

/**
 * 根據標籤篩選 FAQ
 */
export function getFAQsByTag(tag: string): FAQ[] {
  const faqs = getAllFAQs();
  return faqs.filter(faq => 
    faq.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
  );
}

/**
 * 根據分類篩選 FAQ
 */
export function getFAQsByCategory(category: string): FAQ[] {
  const faqs = getAllFAQs();
  return faqs.filter(faq => faq.category === category);
}

/**
 * 獲取所有標籤
 */
export function getAllTags(): string[] {
  if (!tagCache) {
    const faqs = getAllFAQs();
    const tagSet = new Set<string>();
    
    faqs.forEach(faq => {
      faq.tags.forEach(tag => tagSet.add(tag));
    });
    
    tagCache = Array.from(tagSet).sort();
  }
  
  return tagCache;
}

/**
 * 獲取所有分類
 */
export function getAllCategories(): string[] {
  const faqs = getAllFAQs();
  const categorySet = new Set<string>();

  faqs.forEach(faq => {
    if (faq.category) {
      categorySet.add(faq.category);
    }
  });

  return Array.from(categorySet).sort();
}

/**
 * 獲取熱門 FAQ（根據優先級）
 */
export function getPopularFAQs(limit: number = 5): FAQ[] {
  const faqs = getAllFAQs();
  return faqs.slice(0, limit);
}

/**
 * 清除快取（用於測試或資料更新）
 */
export function clearCache(): void {
  faqCache = null;
  tagCache = null;
}
