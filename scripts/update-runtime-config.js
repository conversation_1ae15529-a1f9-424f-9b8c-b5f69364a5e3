#!/usr/bin/env node

/**
 * 更新所有 API 路由的 runtime 配置
 * 從 'edge' 改為 'experimental-edge'（Cloudflare Pages 要求）
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class RuntimeConfigUpdater {
  constructor() {
    this.apiDir = path.join(process.cwd(), 'src/app/api');
    this.processedFiles = [];
    this.skippedFiles = [];
    this.errorFiles = [];
  }

  async run() {
    console.log('🚀 開始更新 Runtime 配置...\n');

    try {
      await this.processDirectory(this.apiDir);
      this.printSummary();
    } catch (error) {
      console.error('❌ 處理失敗:', error.message);
      process.exit(1);
    }
  }

  async processDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await this.processDirectory(fullPath);
        } else if (entry.name === 'route.ts') {
          await this.processRouteFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`❌ 無法讀取目錄 ${dir}:`, error.message);
    }
  }

  async processRouteFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const routeName = this.getRouteNameFromPath(filePath);
      
      console.log(`🔧 處理路由: ${this.getRelativePath(filePath)}`);
      
      let updatedContent = content;
      let hasChanges = false;

      // 1. 更新現有的 runtime = 'edge' 為 'experimental-edge'
      if (content.includes("runtime = 'edge'")) {
        updatedContent = updatedContent.replace(/runtime = 'edge'/g, "runtime = 'experimental-edge'");
        hasChanges = true;
        console.log(`  ✅ 已更新 runtime 配置: 'edge' → 'experimental-edge'`);
      }

      // 2. 添加 Edge Runtime 配置（如果完全沒有的話）
      if (!content.includes("runtime = ") && !content.includes('runtime=')) {
        updatedContent = this.addExperimentalEdgeRuntime(updatedContent);
        if (updatedContent !== content) {
          hasChanges = true;
          console.log(`  ✅ 已添加 experimental-edge Runtime 配置`);
        }
      }

      // 如果有變更，寫入檔案
      if (hasChanges) {
        await fs.writeFile(filePath, updatedContent, 'utf8');
        this.processedFiles.push(filePath);
        console.log(`  💾 已儲存變更\n`);
      } else {
        this.skippedFiles.push(filePath);
        console.log(`  ⏭️ 無需變更\n`);
      }

    } catch (error) {
      console.error(`❌ 處理檔案失敗 ${this.getRelativePath(filePath)}:`, error.message);
      this.errorFiles.push(filePath);
    }
  }

  addExperimentalEdgeRuntime(content) {
    // 尋找第一個 export function 或 export async function
    const exportFunctionRegex = /^export\s+(async\s+)?function\s+(GET|POST|PUT|DELETE|PATCH)/m;
    const match = content.match(exportFunctionRegex);
    
    if (match) {
      const insertPosition = match.index;
      
      // 在第一個 export function 前插入 Edge Runtime 配置
      const beforeFunction = content.substring(0, insertPosition);
      const afterFunction = content.substring(insertPosition);
      
      // 檢查前面是否已經有空行，如果沒有則添加
      const needsNewlineBefore = !beforeFunction.endsWith('\n\n');
      const edgeRuntimeConfig = `${needsNewlineBefore ? '\n' : ''}// Edge Runtime 配置 - Cloudflare Pages 需要\nexport const runtime = 'experimental-edge';\n\n`;
      
      return beforeFunction + edgeRuntimeConfig + afterFunction;
    }
    
    return content;
  }

  getRouteNameFromPath(filePath) {
    const relativePath = path.relative(this.apiDir, filePath);
    return path.dirname(relativePath).replace(/\\/g, '/');
  }

  getRelativePath(filePath) {
    return path.relative(process.cwd(), filePath);
  }

  printSummary() {
    console.log('\n📊 處理摘要:');
    console.log(`✅ 已處理: ${this.processedFiles.length} 個檔案`);
    console.log(`⏭️ 已跳過: ${this.skippedFiles.length} 個檔案`);
    console.log(`❌ 錯誤: ${this.errorFiles.length} 個檔案`);

    if (this.processedFiles.length > 0) {
      console.log('\n已處理的檔案:');
      this.processedFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }

    if (this.errorFiles.length > 0) {
      console.log('\n錯誤檔案:');
      this.errorFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }

    console.log('\n🎉 Runtime 配置更新完成！');
    console.log('💡 所有 API 路由現在使用 experimental-edge runtime，符合 Cloudflare Pages 要求。');
  }
}

// 執行更新
const updater = new RuntimeConfigUpdater();
updater.run().catch(console.error);
