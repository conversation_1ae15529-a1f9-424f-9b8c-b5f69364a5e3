#!/usr/bin/env node

/**
 * 修復 Cloudflare Pages Edge Runtime 兼容性問題
 * 1. 為所有 API 路由添加 Edge Runtime 配置
 * 2. 將 Google Sheets 相關的導入和調用替換為 Edge Runtime 兼容版本
 */

const fs = require('fs').promises;
const path = require('path');

class EdgeRuntimeFixer {
  constructor() {
    this.apiDir = path.join(process.cwd(), 'src/app/api');
    this.processedFiles = [];
    this.skippedFiles = [];
    this.errorFiles = [];
  }

  async run() {
    console.log('🚀 開始修復 Edge Runtime 兼容性問題...\n');

    try {
      await this.processDirectory(this.apiDir);
      this.printSummary();
    } catch (error) {
      console.error('❌ 處理失敗:', error.message);
      process.exit(1);
    }
  }

  async processDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await this.processDirectory(fullPath);
        } else if (entry.name === 'route.ts') {
          await this.processRouteFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`❌ 無法讀取目錄 ${dir}:`, error.message);
    }
  }

  async processRouteFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const routeName = this.getRouteNameFromPath(filePath);
      
      console.log(`🔧 處理路由: ${this.getRelativePath(filePath)}`);
      
      let updatedContent = content;
      let hasChanges = false;

      // 1. 添加 Edge Runtime 配置（如果沒有的話）
      if (!content.includes("runtime = 'edge'") && !content.includes('runtime="edge"')) {
        updatedContent = this.addEdgeRuntime(updatedContent);
        if (updatedContent !== content) {
          hasChanges = true;
          console.log(`  ✅ 已添加 Edge Runtime 配置`);
        }
      }

      // 2. 替換 Google Sheets 相關的導入
      const googleSheetsImports = [
        { old: "import { getSheetsClient } from '@/lib/google-sheets';", new: "import { EdgeSheetsClient } from '@/lib/google-sheets-edge';" },
        { old: "import { getSheetData } from '@/lib/google-sheets';", new: "import { getSheetDataEdge } from '@/lib/google-sheets-edge';\nimport { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';" },
        { old: "import { appendToSheet } from '@/lib/google-sheets';", new: "import { appendSheetDataEdge } from '@/lib/google-sheets-edge';\nimport { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';" },
        { old: "import { updateSheetData } from '@/lib/google-sheets';", new: "import { updateSheetDataEdge } from '@/lib/google-sheets-edge';\nimport { GOOGLE_SHEETS_CONFIG } from '@/config/environment-config';" }
      ];

      for (const importReplace of googleSheetsImports) {
        if (updatedContent.includes(importReplace.old)) {
          updatedContent = updatedContent.replace(importReplace.old, importReplace.new);
          hasChanges = true;
          console.log(`  ✅ 已替換導入: ${importReplace.old.split("'")[1]}`);
        }
      }

      // 3. 替換 Google Sheets 相關的函數調用
      const functionReplacements = [
        // getSheetsClient() 相關
        {
          pattern: /const sheets = getSheetsClient\(\);/g,
          replacement: '// 使用 Edge Runtime 兼容的 Google Sheets 客戶端'
        },
        {
          pattern: /await sheets\.spreadsheets\.values\.append\(\{[\s\S]*?\}\);/g,
          replacement: (match) => {
            // 提取 spreadsheetId 和 range
            const spreadsheetIdMatch = match.match(/spreadsheetId:\s*([^,\n]+)/);
            const rangeMatch = match.match(/range:\s*['"`]([^'"`]+)['"`]/);
            
            if (spreadsheetIdMatch && rangeMatch) {
              const spreadsheetId = spreadsheetIdMatch[1].trim();
              const range = rangeMatch[1];
              return `await appendSheetDataEdge(${spreadsheetId}, '${range}', [sheetData]);`;
            }
            return match;
          }
        },
        // getSheetData 相關
        {
          pattern: /await getSheetData\(['"`]([^'"`]+)['"`]\)/g,
          replacement: 'await getSheetDataEdge(GOOGLE_SHEETS_CONFIG.getSheetId(), \'$1\')'
        },
        // appendToSheet 相關
        {
          pattern: /await appendToSheet\(['"`]([^'"`]+)['"`],\s*\[([^\]]+)\]\)/g,
          replacement: 'await appendSheetDataEdge(GOOGLE_SHEETS_CONFIG.getSheetId(), \'$1\', [$2])'
        }
      ];

      for (const funcReplace of functionReplacements) {
        if (typeof funcReplace.replacement === 'function') {
          const matches = [...updatedContent.matchAll(funcReplace.pattern)];
          if (matches.length > 0) {
            updatedContent = updatedContent.replace(funcReplace.pattern, funcReplace.replacement);
            hasChanges = true;
            console.log(`  ✅ 已替換函數調用 (${matches.length} 處)`);
          }
        } else {
          if (funcReplace.pattern.test(updatedContent)) {
            updatedContent = updatedContent.replace(funcReplace.pattern, funcReplace.replacement);
            hasChanges = true;
            console.log(`  ✅ 已替換函數調用`);
          }
        }
      }

      // 如果有變更，寫入檔案
      if (hasChanges) {
        await fs.writeFile(filePath, updatedContent, 'utf8');
        this.processedFiles.push(filePath);
        console.log(`  💾 已儲存變更\n`);
      } else {
        this.skippedFiles.push(filePath);
        console.log(`  ⏭️ 無需變更\n`);
      }

    } catch (error) {
      console.error(`❌ 處理檔案失敗 ${this.getRelativePath(filePath)}:`, error.message);
      this.errorFiles.push(filePath);
    }
  }

  addEdgeRuntime(content) {
    // 尋找第一個 export function 或 export async function
    const exportFunctionRegex = /^export\s+(async\s+)?function\s+(GET|POST|PUT|DELETE|PATCH)/m;
    const match = content.match(exportFunctionRegex);
    
    if (match) {
      const insertPosition = match.index;
      
      // 在第一個 export function 前插入 Edge Runtime 配置
      const beforeFunction = content.substring(0, insertPosition);
      const afterFunction = content.substring(insertPosition);
      
      // 檢查前面是否已經有空行，如果沒有則添加
      const needsNewlineBefore = !beforeFunction.endsWith('\n\n');
      const edgeRuntimeConfig = `${needsNewlineBefore ? '\n' : ''}// Edge Runtime 配置 - Cloudflare Pages 需要\nexport const runtime = 'edge';\n\n`;
      
      return beforeFunction + edgeRuntimeConfig + afterFunction;
    }
    
    return content;
  }

  getRouteNameFromPath(filePath) {
    const relativePath = path.relative(this.apiDir, filePath);
    return path.dirname(relativePath).replace(/\\/g, '/');
  }

  getRelativePath(filePath) {
    return path.relative(process.cwd(), filePath);
  }

  printSummary() {
    console.log('\n📊 處理摘要:');
    console.log(`✅ 已處理: ${this.processedFiles.length} 個檔案`);
    console.log(`⏭️ 已跳過: ${this.skippedFiles.length} 個檔案`);
    console.log(`❌ 錯誤: ${this.errorFiles.length} 個檔案`);

    if (this.processedFiles.length > 0) {
      console.log('\n已處理的檔案:');
      this.processedFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }

    if (this.errorFiles.length > 0) {
      console.log('\n錯誤檔案:');
      this.errorFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }

    console.log('\n🎉 Edge Runtime 修復完成！');
  }
}

// 執行修復
const fixer = new EdgeRuntimeFixer();
fixer.run().catch(console.error);
