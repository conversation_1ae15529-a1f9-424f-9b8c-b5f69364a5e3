#!/usr/bin/env node

/**
 * 智能管理 API 路由的 Edge Runtime 配置
 * 只為不使用 Node.js 模組的 API 路由添加 Edge Runtime
 * 解決 Cloudflare Pages 建置問題
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class EdgeRuntimeManager {
  constructor() {
    this.apiDir = path.join(__dirname, '../src/app/api');
    this.processedFiles = [];
    this.skippedFiles = [];
    this.errors = [];

    // API 路由黑名單：使用 Node.js 模組，不能使用 Edge Runtime
    this.nodeJsOnlyRoutes = [
      'contact',                    // 使用 Google Sheets
      'event-registration',         // 使用 Google Sheets
      'register',                   // 使用 Google Sheets
      'pangea-appointment',         // 使用 Google Sheets
      'watch-appointment',          // 使用 Google Sheets
      'support',                    // 使用 Google Sheets
      'blog',                       // 使用 Google Sheets + MDX (fs, path)
      'blog/[slug]',                // 使用 MDX (fs, path)
      'update-atm-order',           // 使用 Google Sheets
      'order/[orderNo]',            // 使用 Google Sheets
      'order-status',               // 使用 Google Sheets
      'admin/cache',                // 使用 Google Sheets (通過 cache-implementation)
      'retry-payment',              // 使用 Google Sheets
      'webhook/payment',            // 使用 Google Sheets
      'pre-owned-watches',          // 使用 Google Sheets (通過 cache-implementation)
      'pre-owned-watches/[slug]',   // 使用 Google Sheets (通過 cache-implementation)
      'cache-performance',          // 使用 Google Sheets (通過 cache-implementation)
      'session-availability',       // 使用 Google Sheets
      'create-payment',             // 使用 PayUni (crypto, querystring)
      'decrypt-payuni',             // 使用 PayUni (crypto)
    ];
  }

  async run() {
    console.log('🚀 開始智能管理 API 路由的 Edge Runtime 配置...\n');

    try {
      await this.processDirectory(this.apiDir);
      this.printSummary();
    } catch (error) {
      console.error('❌ 處理失敗:', error.message);
      process.exit(1);
    }
  }

  async processDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          await this.processDirectory(fullPath);
        } else if (entry.name === 'route.ts') {
          await this.processRouteFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`❌ 無法讀取目錄 ${dir}:`, error.message);
    }
  }

  async processRouteFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const routeName = this.getRouteNameFromPath(filePath);
      const shouldUseEdgeRuntime = !this.nodeJsOnlyRoutes.includes(routeName);
      const hasEdgeRuntime = content.includes("runtime = 'edge'") || content.includes('runtime="edge"');

      if (shouldUseEdgeRuntime && !hasEdgeRuntime) {
        // 需要添加 Edge Runtime
        const updatedContent = this.addEdgeRuntime(content);
        if (updatedContent !== content) {
          await fs.writeFile(filePath, updatedContent, 'utf8');
          console.log(`✅ 已添加 Edge Runtime: ${this.getRelativePath(filePath)}`);
          this.processedFiles.push(filePath);
        } else {
          console.log(`⚠️ 無法添加 Edge Runtime: ${this.getRelativePath(filePath)} (找不到合適的插入位置)`);
          this.skippedFiles.push(filePath);
        }
      } else if (!shouldUseEdgeRuntime && hasEdgeRuntime) {
        // 需要移除 Edge Runtime
        const updatedContent = this.removeEdgeRuntime(content);
        if (updatedContent !== content) {
          await fs.writeFile(filePath, updatedContent, 'utf8');
          console.log(`🗑️ 已移除 Edge Runtime: ${this.getRelativePath(filePath)} (使用 Node.js 模組)`);
          this.processedFiles.push(filePath);
        } else {
          console.log(`⚠️ 無法移除 Edge Runtime: ${this.getRelativePath(filePath)}`);
          this.skippedFiles.push(filePath);
        }
      } else {
        // 狀態正確，無需修改
        const status = shouldUseEdgeRuntime ? '✅ Edge Runtime' : '🟡 Node.js Runtime';
        console.log(`⏭️ 跳過 ${this.getRelativePath(filePath)} (${status})`);
        this.skippedFiles.push(filePath);
      }
    } catch (error) {
      console.error(`❌ 處理檔案 ${filePath} 失敗:`, error.message);
      this.errors.push({ file: filePath, error: error.message });
    }
  }

  addEdgeRuntime(content) {
    // 尋找第一個 export function 或 export async function
    const exportFunctionRegex = /^export\s+(async\s+)?function\s+(GET|POST|PUT|DELETE|PATCH)/m;
    const match = content.match(exportFunctionRegex);
    
    if (match) {
      const insertPosition = match.index;
      
      // 在第一個 export function 前插入 Edge Runtime 配置
      const beforeFunction = content.substring(0, insertPosition);
      const afterFunction = content.substring(insertPosition);
      
      // 檢查前面是否已經有空行，如果沒有則添加
      const needsNewlineBefore = !beforeFunction.endsWith('\n\n');
      const edgeRuntimeConfig = `${needsNewlineBefore ? '\n' : ''}// Edge Runtime 配置 - Cloudflare Pages 需要\nexport const runtime = 'edge';\n\n`;
      
      return beforeFunction + edgeRuntimeConfig + afterFunction;
    }
    
    return content;
  }

  getRelativePath(filePath) {
    return path.relative(path.join(__dirname, '..'), filePath);
  }

  getRouteNameFromPath(filePath) {
    // 從路徑提取路由名稱，例如：
    // /path/to/src/app/api/contact/route.ts -> contact
    // /path/to/src/app/api/event-registration/route.ts -> event-registration
    const relativePath = path.relative(this.apiDir, filePath);
    const routeName = path.dirname(relativePath);
    return routeName === '.' ? '' : routeName;
  }

  removeEdgeRuntime(content) {
    // 移除 Edge Runtime 配置行
    const lines = content.split('\n');
    const filteredLines = lines.filter(line => {
      const trimmed = line.trim();
      return !(
        trimmed.includes("runtime = 'edge'") ||
        trimmed.includes('runtime="edge"') ||
        trimmed.includes('// Edge Runtime 配置') ||
        (trimmed.startsWith('//') && trimmed.includes('Cloudflare Pages'))
      );
    });

    // 移除多餘的空行
    let result = filteredLines.join('\n');
    result = result.replace(/\n\n\n+/g, '\n\n'); // 將多個連續空行替換為兩個空行

    return result;
  }

  printSummary() {
    console.log('\n📊 處理結果摘要:');
    console.log(`✅ 已處理: ${this.processedFiles.length} 個檔案`);
    console.log(`⏭️ 已跳過: ${this.skippedFiles.length} 個檔案`);
    console.log(`❌ 錯誤: ${this.errors.length} 個檔案`);
    
    if (this.processedFiles.length > 0) {
      console.log('\n✅ 已處理的檔案:');
      this.processedFiles.forEach(file => {
        console.log(`  - ${this.getRelativePath(file)}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 處理失敗的檔案:');
      this.errors.forEach(({ file, error }) => {
        console.log(`  - ${this.getRelativePath(file)}: ${error}`);
      });
    }
    
    console.log('\n🎉 Edge Runtime 配置添加完成！');
    console.log('現在可以重新推送代碼到 Cloudflare Pages 進行建置。');
  }
}

// 執行腳本
const manager = new EdgeRuntimeManager();
manager.run().catch(console.error);
