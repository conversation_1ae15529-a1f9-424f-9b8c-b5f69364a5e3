import type { NextConfig } from "next";
import createMDX from '@next/mdx';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';

const nextConfig: NextConfig = {
  // 支援 MDX 檔案作為頁面
  pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'objectstorage.ap-tokyo-1.oraclecloud.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'drive.google.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'imgur.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'i.imgur.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
        port: '',
        pathname: '/**',
      },
      // 允許任何 HTTPS 圖片（開發用，生產環境建議限制特定網域）
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

// MDX 配置
const withMDX = createMDX({
  options: {
    remarkPlugins: [remarkGfm], // 支援 GitHub Flavored Markdown
    rehypePlugins: [rehypeHighlight], // 程式碼語法高亮
  },
});

export default withMDX(nextConfig);
